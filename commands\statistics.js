/**
 * أمر الإحصائيات الموحد - يجمع جميع وظائف الإحصائيات والنقاط في أمر واحد
 */

const { SlashCommandBuilder } = require('discord.js');
const logger = require('../modules/core/logger');

// إنشاء بيانات الأمر الموحد
const commandData = new SlashCommandBuilder()
    .setName('statistics')
    .setDescription('إدارة الإحصائيات والنقاط - جميع وظائف النقاط في مكان واحد')

    // أمر فرعي: عرض الإحصائيات
    .addSubcommand(subcommand =>
        subcommand
            .setName('view')
            .setDescription('عرض إحصائيات لاعب')
            .addUserOption(option =>
                option.setName('player')
                    .setDescription('اللاعب (اتركه فارغًا لعرض إحصائياتك)')
                    .setRequired(false)
            )
            .addStringOption(option =>
                option.setName('type')
                    .setDescription('نوع الإحصائيات')
                    .setRequired(false)
                    .addChoices(
                        { name: '📊 إحصائيات عامة', value: 'general' },
                        { name: '🏆 إحصائيات البطولات', value: 'tournaments' },
                        { name: '👥 إحصائيات الزملاء', value: 'teammates' },
                        { name: '📈 تاريخ النقاط', value: 'history' }
                    )
            )
    )

    // أمر فرعي: إضافة نقاط
    .addSubcommand(subcommand =>
        subcommand
            .setName('addpoints')
            .setDescription('إضافة نقاط للاعب (للمشرفين)')
            .addUserOption(option =>
                option.setName('player')
                    .setDescription('اللاعب')
                    .setRequired(true)
            )
            .addIntegerOption(option =>
                option.setName('points')
                    .setDescription('عدد النقاط')
                    .setRequired(true)
                    .setMinValue(1)
                    .setMaxValue(100)
            )
            .addStringOption(option =>
                option.setName('reason')
                    .setDescription('سبب إضافة النقاط')
                    .setRequired(false)
            )
    )

    // أمر فرعي: إزالة نقاط
    .addSubcommand(subcommand =>
        subcommand
            .setName('removepoints')
            .setDescription('إزالة نقاط من لاعب (للمشرفين)')
            .addUserOption(option =>
                option.setName('player')
                    .setDescription('اللاعب')
                    .setRequired(true)
            )
            .addIntegerOption(option =>
                option.setName('points')
                    .setDescription('عدد النقاط')
                    .setRequired(true)
                    .setMinValue(1)
                    .setMaxValue(100)
            )
            .addStringOption(option =>
                option.setName('reason')
                    .setDescription('سبب إزالة النقاط')
                    .setRequired(false)
            )
    )

    // أمر فرعي: تسجيل انتصار
    .addSubcommand(subcommand =>
        subcommand
            .setName('booyah')
            .setDescription('تسجيل انتصار (Booyah)')
            .addStringOption(option =>
                option.setName('teammates')
                    .setDescription('الزملاء في المباراة (أسماء مفصولة بفواصل)')
                    .setRequired(false)
            )
    )

    // أمر فرعي: تسجيل هزيمة
    .addSubcommand(subcommand =>
        subcommand
            .setName('tkfat')
            .setDescription('تسجيل هزيمة (Tkfat)')
            .addStringOption(option =>
                option.setName('teammates')
                    .setDescription('الزملاء في المباراة (أسماء مفصولة بفواصل)')
                    .setRequired(false)
            )
    );

/**
 * تنفيذ الأمر الموحد للإحصائيات
 * @param {Object} interaction - كائن التفاعل
 */
async function execute(interaction) {
    try {
        const subcommand = interaction.options.getSubcommand();

        // توجيه الأمر إلى المعالج المناسب
        switch (subcommand) {
            case 'view':
                await handleViewStats(interaction);
                break;
            case 'addpoints':
                await handleAddPoints(interaction);
                break;
            case 'removepoints':
                await handleRemovePoints(interaction);
                break;
            case 'booyah':
                await handleBooyah(interaction);
                break;
            case 'tkfat':
                await handleTkfat(interaction);
                break;
            default:
                await interaction.reply({
                    content: '⚠️ أمر فرعي غير معروف.',
                    ephemeral: true
                });
        }

        logger.info(`تم تنفيذ الأمر statistics ${subcommand} بواسطة ${interaction.user.tag}`);
    } catch (error) {
        logger.error(`خطأ في تنفيذ الأمر statistics ${interaction.options.getSubcommand()}:`, error);

        const errorMessage = `⚠️ حدث خطأ أثناء تنفيذ الأمر: ${error.message}`;

        if (interaction.deferred || interaction.replied) {
            await interaction.editReply({ content: errorMessage }).catch(console.error);
        } else {
            await interaction.reply({ content: errorMessage, ephemeral: true }).catch(console.error);
        }
    }
}

/**
 * معالجة أمر عرض الإحصائيات
 * @param {Object} interaction - كائن التفاعل
 */
async function handleViewStats(interaction) {
    // إنشاء interaction مؤقت للأمر الأصلي مع جميع الدوال والخصائص المطلوبة
    const tempInteraction = {
        ...interaction,
        options: {
            getUser: (name) => {
                if (name === 'user') return interaction.options.getUser('player');
                return interaction.options.getUser(name);
            },
            getString: (name) => {
                if (name === 'type') return interaction.options.getString('type');
                return interaction.options.getString(name);
            }
        },
        // نسخ جميع الدوال المطلوبة
        deferReply: interaction.deferReply.bind(interaction),
        reply: interaction.reply.bind(interaction),
        editReply: interaction.editReply.bind(interaction),
        followUp: interaction.followUp.bind(interaction)
    };

    // استيراد الدالة من الأمر الأصلي
    const originalCommand = require('./stats');
    await originalCommand.execute(tempInteraction);
}

/**
 * معالجة أمر إضافة النقاط
 * @param {Object} interaction - كائن التفاعل
 */
async function handleAddPoints(interaction) {
    // إنشاء interaction مؤقت للأمر الأصلي مع تعديل اسم المعامل
    const tempInteraction = {
        ...interaction,
        options: {
            getUser: (name) => {
                if (name === 'user') return interaction.options.getUser('player');
                return interaction.options.getUser(name);
            },
            getInteger: (name) => interaction.options.getInteger(name),
            getString: (name) => interaction.options.getString(name)
        }
    };

    // استيراد الدالة من الأمر الأصلي
    const originalCommand = require('./addpoints');
    await originalCommand.execute(tempInteraction);
}

/**
 * معالجة أمر إزالة النقاط
 * @param {Object} interaction - كائن التفاعل
 */
async function handleRemovePoints(interaction) {
    // إنشاء interaction مؤقت للأمر الأصلي مع تعديل اسم المعامل
    const tempInteraction = {
        ...interaction,
        options: {
            getUser: (name) => {
                if (name === 'user') return interaction.options.getUser('player');
                return interaction.options.getUser(name);
            },
            getInteger: (name) => interaction.options.getInteger(name),
            getString: (name) => interaction.options.getString(name)
        }
    };

    // استيراد الدالة من الأمر الأصلي
    const originalCommand = require('./removepoints');
    await originalCommand.execute(tempInteraction);
}

/**
 * معالجة أمر تسجيل الانتصار
 * @param {Object} interaction - كائن التفاعل
 */
async function handleBooyah(interaction) {
    // إنشاء interaction مؤقت للأمر الأصلي
    const tempInteraction = {
        ...interaction,
        options: {
            getString: (name) => {
                if (name === 'teammates') return interaction.options.getString('teammates');
                return interaction.options.getString(name);
            }
        }
    };

    // استيراد الدالة من الأمر الأصلي
    const originalCommand = require('./booyah');
    await originalCommand.execute(tempInteraction);
}

/**
 * معالجة أمر تسجيل الهزيمة
 * @param {Object} interaction - كائن التفاعل
 */
async function handleTkfat(interaction) {
    // إنشاء interaction مؤقت للأمر الأصلي
    const tempInteraction = {
        ...interaction,
        options: {
            getString: (name) => {
                if (name === 'teammates') return interaction.options.getString('teammates');
                return interaction.options.getString(name);
            }
        }
    };

    // استيراد الدالة من الأمر الأصلي
    const originalCommand = require('./tkfat');
    await originalCommand.execute(tempInteraction);
}

// تصدير الأمر
module.exports = {
    data: commandData,
    execute
};
