/**
 * وحدة معالجة أزرار لوحة التحكم
 */

const logger = require('../../core/logger');
const { getTournamentInfo } = require('../../tournaments/tournament');
const { createTournamentDashboard } = require('../tournament-dashboard');

/**
 * معالجة أزرار لوحة التحكم
 * @param {Object} interaction - كائن التفاعل
 */
async function handleDashboardButton(interaction) {
    const customId = interaction.customId;

    // معالجة أزرار لوحة تحكم البطولة
    if (customId.startsWith('tournament_dashboard_')) {
        await handleTournamentDashboardButton(interaction);
    }
    // معالجة أزرار مشاركة البطولة
    else if (customId.startsWith('share_tournament_')) {
        await handleShareTournamentButton(interaction);
    }
    // معالجة أزرار تحديث ترتيب البطولة
    else if (customId.startsWith('refresh_leaderboard_')) {
        await handleRefreshLeaderboardButton(interaction);
    }
    // معالجة أزرار البطولات الجديدة
    else if (customId.startsWith('tournament_dashboard_')) {
        await handleTournamentDashboardButton(interaction);
    }
    else if (customId === 'create_tournament_wizard') {
        await handleCreateTournamentWizardButton(interaction);
    }
    else if (customId === 'refresh_tournaments') {
        await handleRefreshTournamentsButton(interaction);
    }
    // معالجة أزرار السيزونات
    else if (customId.startsWith('season_')) {
        const { handleSeasonButton } = require('./season-buttons');
        await handleSeasonButton(interaction);
    }
    // معالجة الأزرار القديمة من النظام السابق
    else if (customId === 'الترتيب' || customId === 'leaderboard') {
        await handleOldLeaderboardButton(interaction);
    }
    else if (customId === 'الإحصائيات' || customId === 'stats') {
        await handleOldStatsButton(interaction);
    }
    else if (customId === 'التاريخ' || customId === 'history') {
        await handleOldHistoryButton(interaction);
    }
    else if (customId === 'التكفات' || customId === 'tkfat') {
        await handleOldTkfatButton(interaction);
    }
    else if (customId === 'توزيع التكفات' || customId === 'distribute_tkfat') {
        await handleOldDistributeTkfatButton(interaction);
    }
    else if (customId === 'تحديث' || customId === 'refresh') {
        await handleOldRefreshButton(interaction);
    }
    else {
        await interaction.reply({
            content: "⚠️ زر غير معروف.",
            ephemeral: true
        });
    }
}

/**
 * معالجة أزرار لوحة تحكم البطولة
 * @param {Object} interaction - كائن التفاعل
 */
async function handleTournamentDashboardButton(interaction) {
    try {
        // استخراج معرف البطولة والتبويب من معرف الزر
        const parts = interaction.customId.split('_');
        const tab = parts[2]; // info, teams, matches, leaderboard
        const tournamentId = parseInt(parts[3]);

        // تأكيد التفاعل
        await interaction.deferUpdate();

        // استدعاء دالة إنشاء لوحة تحكم البطولة
        const result = await createTournamentDashboard(interaction, tournamentId, tab);

        if (!result.success) {
            return interaction.followUp({
                content: `⚠️ ${result.message}`,
                ephemeral: true
            });
        }

        // تحديث الرسالة
        await interaction.editReply({
            embeds: result.embeds,
            components: result.components
        });

        logger.info(`تم عرض لوحة تحكم البطولة ${tournamentId} (التبويب: ${tab}) بواسطة ${interaction.user.tag}`);
    } catch (error) {
        logger.error('خطأ في معالجة زر لوحة تحكم البطولة:', error);

        try {
            if (interaction.deferred) {
                await interaction.editReply({
                    content: `⚠️ حدث خطأ أثناء معالجة الزر: ${error.message}`,
                    components: []
                });
            } else {
                await interaction.reply({
                    content: `⚠️ حدث خطأ أثناء معالجة الزر: ${error.message}`,
                    ephemeral: true
                });
            }
        } catch (replyError) {
            logger.error('خطأ في الرد على التفاعل:', replyError);
        }
    }
}

/**
 * معالجة أزرار مشاركة البطولة
 * @param {Object} interaction - كائن التفاعل
 */
async function handleShareTournamentButton(interaction) {
    try {
        // استخراج معرف البطولة
        const tournamentId = parseInt(interaction.customId.replace('share_tournament_', ''));

        // الحصول على معلومات البطولة
        const tournament = getTournamentInfo(tournamentId);

        if (!tournament) {
            return interaction.reply({
                content: `⚠️ لم يتم العثور على بطولة بالمعرف ${tournamentId}.`,
                ephemeral: true
            });
        }

        // الحصول على نوع البطولة بالعربية
        let tournamentTypeArabic;
        switch (tournament.type) {
            case 'knockout':
                tournamentTypeArabic = 'خروج المغلوب';
                break;
            case 'league':
                tournamentTypeArabic = 'دوري';
                break;
            case 'battle_royale':
                tournamentTypeArabic = 'باتل رويال';
                break;
            default:
                tournamentTypeArabic = tournament.type;
        }

        // الحصول على حالة البطولة بالعربية
        let statusArabic;
        switch (tournament.status) {
            case 'registration':
                statusArabic = 'التسجيل';
                break;
            case 'in_progress':
                statusArabic = 'جارية';
                break;
            case 'completed':
                statusArabic = 'مكتملة';
                break;
            case 'cancelled':
                statusArabic = 'ملغية';
                break;
            default:
                statusArabic = tournament.status;
        }

        // إنشاء رسالة المشاركة
        const message = `🏆 **بطولة: ${tournament.name}**\n\n` +
            `📋 **النوع:** ${tournamentTypeArabic}\n` +
            `🔄 **الحالة:** ${statusArabic}\n` +
            `👥 **الفرق:** ${tournament.teams.length}/${tournament.maxTeams}\n` +
            `👤 **اللاعبين في الفريق:** ${tournament.playersPerTeam}\n` +
            `📅 **التاريخ:** <t:${Math.floor(new Date(tournament.startDate).getTime() / 1000)}:D> إلى <t:${Math.floor(new Date(tournament.endDate).getTime() / 1000)}:D>\n\n` +
            `${tournament.description || ''}\n\n` +
            `استخدم الأمر \`/tournament-dashboard ${tournamentId}\` لعرض لوحة تحكم البطولة.`;

        // إرسال رسالة المشاركة في القناة
        await interaction.channel.send(message);

        // إرسال تأكيد للمستخدم
        await interaction.reply({
            content: '✅ تمت مشاركة معلومات البطولة في القناة.',
            ephemeral: true
        });

        logger.info(`تمت مشاركة معلومات البطولة ${tournamentId} بواسطة ${interaction.user.tag}`);
    } catch (error) {
        logger.error('خطأ في معالجة زر مشاركة البطولة:', error);

        await interaction.reply({
            content: `⚠️ حدث خطأ أثناء مشاركة البطولة: ${error.message}`,
            ephemeral: true
        }).catch(console.error);
    }
}

/**
 * معالجة أزرار تحديث ترتيب البطولة
 * @param {Object} interaction - كائن التفاعل
 */
async function handleRefreshLeaderboardButton(interaction) {
    try {
        // استخراج معرف البطولة
        const tournamentId = parseInt(interaction.customId.replace('refresh_leaderboard_', ''));

        // تأكيد التفاعل
        await interaction.deferUpdate();

        // استدعاء دالة إنشاء لوحة تحكم البطولة مع تبويب الترتيب
        const result = await createTournamentDashboard(interaction, tournamentId, 'leaderboard');

        if (!result.success) {
            return interaction.followUp({
                content: `⚠️ ${result.message}`,
                ephemeral: true
            });
        }

        // تحديث الرسالة
        await interaction.editReply({
            embeds: result.embeds,
            components: result.components
        });

        logger.info(`تم تحديث ترتيب البطولة ${tournamentId} بواسطة ${interaction.user.tag}`);
    } catch (error) {
        logger.error('خطأ في معالجة زر تحديث ترتيب البطولة:', error);

        try {
            if (interaction.deferred) {
                await interaction.editReply({
                    content: `⚠️ حدث خطأ أثناء تحديث الترتيب: ${error.message}`,
                    components: []
                });
            } else {
                await interaction.reply({
                    content: `⚠️ حدث خطأ أثناء تحديث الترتيب: ${error.message}`,
                    ephemeral: true
                });
            }
        } catch (replyError) {
            logger.error('خطأ في الرد على التفاعل:', replyError);
        }
    }
}

/**
 * معالجة زر إنشاء بطولة جديدة (معالج تفاعلي)
 * @param {Object} interaction - كائن التفاعل
 */
async function handleCreateTournamentWizardButton(interaction) {
    try {
        await interaction.reply({
            content: '🚀 **معالج إنشاء البطولة**\n\n' +
                    'استخدم الأمر `/tournament create` لإنشاء بطولة جديدة مع خيارات متقدمة.\n\n' +
                    '**الخطوات:**\n' +
                    '1️⃣ اختر اسم البطولة\n' +
                    '2️⃣ حدد نوع البطولة (خروج المغلوب، دوري، باتل رويال)\n' +
                    '3️⃣ اختر عدد الفرق واللاعبين\n' +
                    '4️⃣ أضف وصف (اختياري)\n' +
                    '5️⃣ تأكيد الإنشاء',
            ephemeral: true
        });

        logger.info(`تم عرض معالج إنشاء البطولة بواسطة ${interaction.user.tag}`);
    } catch (error) {
        logger.error('خطأ في معالجة زر معالج إنشاء البطولة:', error);

        await interaction.reply({
            content: `⚠️ حدث خطأ أثناء معالجة الزر: ${error.message}`,
            ephemeral: true
        }).catch(console.error);
    }
}

/**
 * معالجة زر تحديث قائمة البطولات
 * @param {Object} interaction - كائن التفاعل
 */
async function handleRefreshTournamentsButton(interaction) {
    try {
        // تأكيد التفاعل
        await interaction.deferUpdate();

        // استدعاء دالة عرض قائمة البطولات النشطة
        const dashboardHandler = require('../../commands/handlers/tournament-dashboard');
        const tempInteraction = {
            ...interaction,
            options: {
                getInteger: () => null // لا يوجد tournament_id
            },
            editReply: interaction.editReply.bind(interaction)
        };

        await dashboardHandler.execute(tempInteraction);

        logger.info(`تم تحديث قائمة البطولات بواسطة ${interaction.user.tag}`);
    } catch (error) {
        logger.error('خطأ في معالجة زر تحديث البطولات:', error);

        try {
            if (interaction.deferred) {
                await interaction.editReply({
                    content: `⚠️ حدث خطأ أثناء تحديث القائمة: ${error.message}`,
                    components: []
                });
            } else {
                await interaction.reply({
                    content: `⚠️ حدث خطأ أثناء تحديث القائمة: ${error.message}`,
                    ephemeral: true
                });
            }
        } catch (replyError) {
            logger.error('خطأ في الرد على التفاعل:', replyError);
        }
    }
}

/**
 * معالجة زر الترتيب القديم
 * @param {Object} interaction - كائن التفاعل
 */
async function handleOldLeaderboardButton(interaction) {
    try {
        await interaction.deferReply({ ephemeral: true });

        // استيراد دالة الترتيب من النظام الجديد
        const { getCurrentTopPlayers } = require('../../seasons/seasons');
        const topPlayers = getCurrentTopPlayers(10);

        if (topPlayers.length === 0) {
            return interaction.editReply({
                content: '📊 لا يوجد لاعبين مسجلين بعد.'
            });
        }

        // إنشاء embed للترتيب
        const { EmbedBuilder } = require('discord.js');
        const embed = new EmbedBuilder()
            .setTitle('🏅 ترتيب اللاعبين')
            .setColor(0xf1c40f)
            .setDescription('أفضل اللاعبين في الموسم الحالي')
            .setTimestamp();

        let leaderboardText = '';
        for (let i = 0; i < Math.min(10, topPlayers.length); i++) {
            const player = topPlayers[i];
            const medal = ['🥇', '🥈', '🥉'][i] || `**${i + 1}.**`;

            leaderboardText += `${medal} <@${player.userId}>\n`;
            leaderboardText += `   🎉 ${player.booyahPoints} بوياه | 💀 ${player.tkfatPoints} تكفات\n`;
            leaderboardText += `   📊 المجموع: ${player.totalPoints}\n\n`;
        }

        embed.setDescription(leaderboardText);

        await interaction.editReply({
            embeds: [embed]
        });

        logger.info(`تم عرض الترتيب بواسطة ${interaction.user.tag}`);
    } catch (error) {
        logger.error('خطأ في معالجة زر الترتيب القديم:', error);
        await interaction.editReply({
            content: `⚠️ حدث خطأ: ${error.message}`
        });
    }
}

/**
 * معالجة زر الإحصائيات القديم
 * @param {Object} interaction - كائن التفاعل
 */
async function handleOldStatsButton(interaction) {
    try {
        await interaction.deferReply({ ephemeral: true });

        // استيراد دالة الإحصائيات من النظام الجديد
        const { calculateCurrentSeasonStats } = require('../../seasons/seasons');
        const stats = calculateCurrentSeasonStats();

        // إنشاء embed للإحصائيات
        const { EmbedBuilder } = require('discord.js');
        const embed = new EmbedBuilder()
            .setTitle('📊 إحصائيات الموسم الحالي')
            .setColor(0x3498db)
            .setDescription('إحصائيات شاملة للموسم الحالي')
            .addFields(
                {
                    name: '👥 إحصائيات اللاعبين',
                    value: `**إجمالي اللاعبين:** ${stats.players.total}\n` +
                           `**لاعبي البوياه:** ${stats.players.booyahPlayers}\n` +
                           `**لاعبي التكفات:** ${stats.players.tkfatPlayers}`,
                    inline: true
                },
                {
                    name: '💎 إحصائيات النقاط',
                    value: `**إجمالي البوياه:** ${stats.points.totalBooyah}\n` +
                           `**إجمالي التكفات:** ${stats.points.totalTkfat}\n` +
                           `**متوسط البوياه:** ${stats.points.avgBooyah}\n` +
                           `**متوسط التكفات:** ${stats.points.avgTkfat}`,
                    inline: true
                },
                {
                    name: '🏆 إحصائيات البطولات',
                    value: `**البطولات المكتملة:** ${stats.tournaments.completed}\n` +
                           `**البطولات النشطة:** ${stats.tournaments.active}\n` +
                           `**إجمالي البطولات:** ${stats.tournaments.total}`,
                    inline: true
                }
            )
            .setTimestamp();

        await interaction.editReply({
            embeds: [embed]
        });

        logger.info(`تم عرض الإحصائيات بواسطة ${interaction.user.tag}`);
    } catch (error) {
        logger.error('خطأ في معالجة زر الإحصائيات القديم:', error);
        await interaction.editReply({
            content: `⚠️ حدث خطأ: ${error.message}`
        });
    }
}

/**
 * معالجة زر التاريخ القديم
 * @param {Object} interaction - كائن التفاعل
 */
async function handleOldHistoryButton(interaction) {
    try {
        await interaction.deferReply({ ephemeral: true });

        // استيراد دالة التاريخ من النظام الجديد
        const { getSeasonsHistory } = require('../../seasons/seasons');
        const result = getSeasonsHistory(10);

        if (!result.success) {
            return interaction.editReply({
                content: `⚠️ ${result.message}`
            });
        }

        const { createSeasonsHistoryEmbed } = require('../season-embeds');
        const embed = createSeasonsHistoryEmbed(result.seasons, result.currentSeason);

        await interaction.editReply({
            embeds: [embed]
        });

        logger.info(`تم عرض تاريخ المواسم بواسطة ${interaction.user.tag}`);
    } catch (error) {
        logger.error('خطأ في معالجة زر التاريخ القديم:', error);
        await interaction.editReply({
            content: `⚠️ حدث خطأ: ${error.message}`
        });
    }
}

/**
 * معالجة زر التكفات القديم
 * @param {Object} interaction - كائن التفاعل
 */
async function handleOldTkfatButton(interaction) {
    try {
        await interaction.deferReply({ ephemeral: true });

        // استيراد دالة ترتيب التكفات
        const { getTkfatLeaderboard } = require('../../points/points');
        const topTkfatPlayers = getTkfatLeaderboard(10);

        if (topTkfatPlayers.length === 0) {
            return interaction.editReply({
                content: '💀 لا يوجد لاعبين في ترتيب التكفات بعد.'
            });
        }

        // إنشاء embed لترتيب التكفات
        const { EmbedBuilder } = require('discord.js');
        const embed = new EmbedBuilder()
            .setTitle('💀 ترتيب التكفات')
            .setColor(0xe74c3c)
            .setDescription('أكثر اللاعبين في نقاط التكفات')
            .setTimestamp();

        let leaderboardText = '';
        for (let i = 0; i < topTkfatPlayers.length; i++) {
            const player = topTkfatPlayers[i];
            const medal = ['💀', '☠️', '👻'][i] || `**${i + 1}.**`;

            leaderboardText += `${medal} <@${player.userId}> - ${player.points} تكفات\n`;
        }

        embed.setDescription(leaderboardText);

        await interaction.editReply({
            embeds: [embed]
        });

        logger.info(`تم عرض ترتيب التكفات بواسطة ${interaction.user.tag}`);
    } catch (error) {
        logger.error('خطأ في معالجة زر التكفات القديم:', error);
        await interaction.editReply({
            content: `⚠️ حدث خطأ: ${error.message}`
        });
    }
}

/**
 * معالجة زر توزيع التكفات القديم
 * @param {Object} interaction - كائن التفاعل
 */
async function handleOldDistributeTkfatButton(interaction) {
    try {
        await interaction.reply({
            content: '🎁 **نظام توزيع التكفات**\n\n' +
                    'هذه الميزة تم دمجها في النظام الجديد!\n\n' +
                    '**استخدم الأوامر الجديدة:**\n' +
                    '• `/season rewards` - لعرض مكافآت الموسم\n' +
                    '• `/season leaderboard type:tkfat` - لترتيب التكفات\n' +
                    '• `/addpoints type:tkfat` - لإضافة نقاط تكفات',
            ephemeral: true
        });

        logger.info(`تم توجيه ${interaction.user.tag} للنظام الجديد`);
    } catch (error) {
        logger.error('خطأ في معالجة زر توزيع التكفات القديم:', error);
        await interaction.reply({
            content: `⚠️ حدث خطأ: ${error.message}`,
            ephemeral: true
        });
    }
}

/**
 * معالجة زر التحديث القديم
 * @param {Object} interaction - كائن التفاعل
 */
async function handleOldRefreshButton(interaction) {
    try {
        await interaction.reply({
            content: '🔄 **تحديث البيانات**\n\n' +
                    'تم تحديث البيانات بنجاح!\n\n' +
                    '**للحصول على أحدث المعلومات:**\n' +
                    '• `/season current` - لوحة التحكم المحدثة\n' +
                    '• `/season stats` - إحصائيات محدثة\n' +
                    '• `/season leaderboard` - ترتيب محدث',
            ephemeral: true
        });

        logger.info(`تم تحديث البيانات بواسطة ${interaction.user.tag}`);
    } catch (error) {
        logger.error('خطأ في معالجة زر التحديث القديم:', error);
        await interaction.reply({
            content: `⚠️ حدث خطأ: ${error.message}`,
            ephemeral: true
        });
    }
}

// تصدير الدوال
module.exports = {
    handleDashboardButton
};
