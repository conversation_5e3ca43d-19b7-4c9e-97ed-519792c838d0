/**
 * وحدة لوحة تحكم السيزونات المحسنة
 * توفر واجهة موحدة وتفاعلية لعرض وإدارة المواسم
 */

const {
    EmbedBuilder,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle
} = require('discord.js');
const logger = require('../core/logger');
const { data } = require('../core/database');

// الألوان والرموز
const COLORS = {
    SEASON: {
        current: 0x00ff00,
        completed: 0x3498db,
        warning: 0xf39c12,
        danger: 0xe74c3c
    }
};

const EMOJIS = {
    SEASON: '🏆',
    CURRENT: '⭐',
    HISTORY: '📜',
    STATS: '📊',
    REWARDS: '🎁',
    LEADERBOARD: '🏅',
    NEW_SEASON: '🚀',
    CALENDAR: '📅',
    TROPHY: '🏆',
    FIRE: '🔥',
    CROWN: '👑',
    CHART: '📈'
};

/**
 * إنشاء لوحة تحكم الموسم الحالي
 * @param {Object} interaction - كائن التفاعل
 * @returns {Object} - كائن يحتوي على الرسائل المضمنة والمكونات
 */
async function createSeasonDashboard(interaction) {
    try {
        // الحصول على معلومات الموسم الحالي
        const currentSeason = data.seasons.current;
        const seasonStartDate = data.seasons.startDate;
        
        // حساب مدة الموسم
        const seasonDuration = Math.floor((new Date() - new Date(seasonStartDate)) / (1000 * 60 * 60 * 24));
        
        // إحصائيات الموسم الحالي
        const seasonStats = await calculateSeasonStats();
        
        // إنشاء العنوان الديناميكي
        const title = `${EMOJIS.SEASON} الموسم ${currentSeason} ${EMOJIS.CURRENT}`;
        
        // إنشاء الوصف الشامل
        const description = createSeasonDescription(seasonDuration, seasonStats);
        
        // إنشاء الحقول التفاعلية
        const fields = createSeasonFields(seasonStats, seasonStartDate);
        
        // إنشاء الرسالة المضمنة الموحدة
        const embed = new EmbedBuilder()
            .setTitle(title)
            .setColor(COLORS.SEASON.current)
            .setDescription(description)
            .addFields(fields)
            .setThumbnail(interaction.client.user.displayAvatarURL())
            .setFooter({
                text: `الموسم ${currentSeason} • آخر تحديث`,
                iconURL: interaction.client.user.displayAvatarURL()
            })
            .setTimestamp();

        // إنشاء الأزرار التفاعلية
        const components = await createSeasonButtons(interaction, currentSeason);

        return { 
            success: true,
            embeds: [embed], 
            components 
        };
    } catch (error) {
        logger.error('خطأ في إنشاء لوحة تحكم الموسم:', error);
        return {
            success: false,
            message: `حدث خطأ: ${error.message}`
        };
    }
}

/**
 * حساب إحصائيات الموسم الحالي
 * @returns {Object} - إحصائيات الموسم
 */
async function calculateSeasonStats() {
    try {
        // عدد اللاعبين النشطين
        const activePlayers = Object.keys(data.scores).length + Object.keys(data.tkfatScores).length;
        const uniquePlayers = new Set([...Object.keys(data.scores), ...Object.keys(data.tkfatScores)]).size;
        
        // إجمالي النقاط
        const totalBooyahPoints = Object.values(data.scores).reduce((sum, points) => sum + points, 0);
        const totalTkfatPoints = Object.values(data.tkfatScores).reduce((sum, points) => sum + points, 0);
        
        // البطولات المكتملة في هذا الموسم
        const completedTournaments = data.tournaments.history.filter(t => 
            t.status === 'completed' && 
            new Date(t.createdAt) >= new Date(data.seasons.startDate)
        ).length;
        
        // البطولات النشطة
        const activeTournaments = data.tournaments.active.length;
        
        // أفضل لاعب في الموسم
        const topBooyahPlayer = Object.entries(data.scores)
            .sort(([,a], [,b]) => b - a)[0];
        
        const topTkfatPlayer = Object.entries(data.tkfatScores)
            .sort(([,a], [,b]) => b - a)[0];
        
        return {
            uniquePlayers,
            totalBooyahPoints,
            totalTkfatPoints,
            completedTournaments,
            activeTournaments,
            topBooyahPlayer: topBooyahPlayer ? { id: topBooyahPlayer[0], points: topBooyahPlayer[1] } : null,
            topTkfatPlayer: topTkfatPlayer ? { id: topTkfatPlayer[0], points: topTkfatPlayer[1] } : null
        };
    } catch (error) {
        logger.error('خطأ في حساب إحصائيات الموسم:', error);
        return {
            uniquePlayers: 0,
            totalBooyahPoints: 0,
            totalTkfatPoints: 0,
            completedTournaments: 0,
            activeTournaments: 0,
            topBooyahPlayer: null,
            topTkfatPlayer: null
        };
    }
}

/**
 * إنشاء وصف شامل للموسم
 * @param {number} seasonDuration - مدة الموسم بالأيام
 * @param {Object} seasonStats - إحصائيات الموسم
 * @returns {string} - الوصف المنسق
 */
function createSeasonDescription(seasonDuration, seasonStats) {
    let description = `**الموسم النشط حالياً** • **${seasonDuration} يوم**\n\n`;
    
    // إضافة شريط التقدم للموسم (افتراضي 90 يوم)
    const maxSeasonDays = 90;
    const progress = Math.min(Math.round((seasonDuration / maxSeasonDays) * 100), 100);
    const progressBar = createProgressBar(progress);
    
    description += `**تقدم الموسم:** ${progressBar} ${progress}%\n`;
    description += `**المدة:** ${seasonDuration} من ${maxSeasonDays} يوم\n\n`;
    
    // إضافة ملخص سريع
    description += `${EMOJIS.FIRE} **${seasonStats.uniquePlayers}** لاعب نشط\n`;
    description += `${EMOJIS.TROPHY} **${seasonStats.completedTournaments}** بطولة مكتملة\n`;
    description += `${EMOJIS.CHART} **${seasonStats.totalBooyahPoints + seasonStats.totalTkfatPoints}** نقطة إجمالية\n\n`;
    
    return description;
}

/**
 * إنشاء شريط تقدم مرئي
 * @param {number} percentage - النسبة المئوية
 * @returns {string} - شريط التقدم
 */
function createProgressBar(percentage) {
    const filled = Math.round(percentage / 10);
    const empty = 10 - filled;
    return '█'.repeat(filled) + '░'.repeat(empty);
}

/**
 * إنشاء حقول تفاعلية للموسم
 * @param {Object} seasonStats - إحصائيات الموسم
 * @param {string} seasonStartDate - تاريخ بداية الموسم
 * @returns {Array} - مصفوفة الحقول
 */
function createSeasonFields(seasonStats, seasonStartDate) {
    const fields = [];
    
    // حقل الإحصائيات الأساسية
    fields.push({
        name: `${EMOJIS.STATS} إحصائيات أساسية`,
        value: `**اللاعبين:** ${seasonStats.uniquePlayers}\n` +
               `**البطولات:** ${seasonStats.completedTournaments} مكتملة، ${seasonStats.activeTournaments} نشطة\n` +
               `**النقاط:** ${seasonStats.totalBooyahPoints} بوياه، ${seasonStats.totalTkfatPoints} تكفات`,
        inline: true
    });
    
    // حقل التواريخ
    fields.push({
        name: `${EMOJIS.CALENDAR} التواريخ`,
        value: `**البداية:** <t:${Math.floor(new Date(seasonStartDate).getTime() / 1000)}:D>\n` +
               `**اليوم:** <t:${Math.floor(Date.now() / 1000)}:D>\n` +
               `**المدة:** ${Math.floor((new Date() - new Date(seasonStartDate)) / (1000 * 60 * 60 * 24))} يوم`,
        inline: true
    });
    
    // حقل أفضل اللاعبين
    let topPlayersValue = '';
    if (seasonStats.topBooyahPlayer) {
        topPlayersValue += `**🎉 أفضل بوياه:** <@${seasonStats.topBooyahPlayer.id}> (${seasonStats.topBooyahPlayer.points})\n`;
    }
    if (seasonStats.topTkfatPlayer) {
        topPlayersValue += `**💀 أكثر تكفات:** <@${seasonStats.topTkfatPlayer.id}> (${seasonStats.topTkfatPlayer.points})`;
    }
    
    if (topPlayersValue) {
        fields.push({
            name: `${EMOJIS.CROWN} أفضل اللاعبين`,
            value: topPlayersValue || 'لا يوجد لاعبين بعد',
            inline: false
        });
    }
    
    return fields;
}

/**
 * إنشاء أزرار تفاعلية للموسم
 * @param {Object} interaction - كائن التفاعل
 * @param {number} currentSeason - رقم الموسم الحالي
 * @returns {Array} - مصفوفة المكونات
 */
async function createSeasonButtons(interaction, currentSeason) {
    const components = [];
    
    // الصف الأول: أزرار التنقل الأساسية
    const mainRow = new ActionRowBuilder();
    
    mainRow.addComponents(
        new ButtonBuilder()
            .setCustomId(`season_leaderboard_${currentSeason}`)
            .setLabel('الترتيب')
            .setStyle(ButtonStyle.Primary)
            .setEmoji(EMOJIS.LEADERBOARD),
        new ButtonBuilder()
            .setCustomId(`season_stats_${currentSeason}`)
            .setLabel('الإحصائيات')
            .setStyle(ButtonStyle.Secondary)
            .setEmoji(EMOJIS.STATS),
        new ButtonBuilder()
            .setCustomId(`season_history`)
            .setLabel('التاريخ')
            .setStyle(ButtonStyle.Secondary)
            .setEmoji(EMOJIS.HISTORY),
        new ButtonBuilder()
            .setCustomId(`season_rewards_${currentSeason}`)
            .setLabel('المكافآت')
            .setStyle(ButtonStyle.Success)
            .setEmoji(EMOJIS.REWARDS)
    );
    
    components.push(mainRow);
    
    // الصف الثاني: أزرار الإدارة (للمشرفين فقط)
    const member = await interaction.guild.members.fetch(interaction.user.id).catch(() => null);
    const { ADMIN_ROLE_ID } = require('../../config/config');
    const isAdmin = member && (member.permissions.has("ManageEvents") || member.roles.cache.has(ADMIN_ROLE_ID));
    
    if (isAdmin) {
        const adminRow = new ActionRowBuilder();
        
        adminRow.addComponents(
            new ButtonBuilder()
                .setCustomId(`season_new_preview`)
                .setLabel('معاينة موسم جديد')
                .setStyle(ButtonStyle.Primary)
                .setEmoji(EMOJIS.NEW_SEASON),
            new ButtonBuilder()
                .setCustomId(`season_distribute_rewards_${currentSeason}`)
                .setLabel('توزيع المكافآت')
                .setStyle(ButtonStyle.Success)
                .setEmoji('💰'),
            new ButtonBuilder()
                .setCustomId(`season_refresh`)
                .setLabel('تحديث')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('🔄')
        );
        
        components.push(adminRow);
    }
    
    return components.slice(0, 5); // Discord يسمح بحد أقصى 5 صفوف
}

// تصدير الدوال
module.exports = {
    createSeasonDashboard
};
