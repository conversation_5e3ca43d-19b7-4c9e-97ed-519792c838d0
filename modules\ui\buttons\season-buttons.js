/**
 * معالج أزرار السيزونات
 */

const logger = require('../../core/logger');
const { createSeasonDashboard } = require('../season-dashboard');
const { getSeasonsHistory, getCurrentTopPlayers } = require('../../seasons/seasons');
const { createSeasonsHistoryEmbed, createSeasonRewardsEmbed } = require('../season-embeds');
const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

/**
 * معالجة أزرار السيزونات
 * @param {Object} interaction - كائن التفاعل
 */
async function handleSeasonButton(interaction) {
    try {
        const customId = interaction.customId;
        
        // معالجة أنواع الأزرار المختلفة
        if (customId.startsWith('season_leaderboard_')) {
            await handleSeasonLeaderboardButton(interaction);
        }
        else if (customId.startsWith('season_stats_')) {
            await handleSeasonStatsButton(interaction);
        }
        else if (customId === 'season_history') {
            await handleSeasonHistoryButton(interaction);
        }
        else if (customId.startsWith('season_rewards_')) {
            await handleSeasonRewardsButton(interaction);
        }
        else if (customId === 'season_new_preview') {
            await handleNewSeasonPreviewButton(interaction);
        }
        else if (customId.startsWith('season_distribute_rewards_')) {
            await handleDistributeRewardsButton(interaction);
        }
        else if (customId === 'season_refresh') {
            await handleSeasonRefreshButton(interaction);
        }
        else {
            await interaction.reply({
                content: '⚠️ زر غير معروف.',
                ephemeral: true
            });
        }
        
        logger.info(`تم معالجة زر السيزون ${customId} بواسطة ${interaction.user.tag}`);
    } catch (error) {
        logger.error('خطأ في معالجة زر السيزون:', error);
        
        if (interaction.deferred || interaction.replied) {
            await interaction.editReply({
                content: `⚠️ حدث خطأ: ${error.message}`
            }).catch(console.error);
        } else {
            await interaction.reply({
                content: `⚠️ حدث خطأ: ${error.message}`,
                ephemeral: true
            }).catch(console.error);
        }
    }
}

/**
 * معالجة زر ترتيب الموسم
 * @param {Object} interaction - كائن التفاعل
 */
async function handleSeasonLeaderboardButton(interaction) {
    try {
        await interaction.deferReply({ ephemeral: true });
        
        const seasonNumber = interaction.customId.split('_')[2];
        const topPlayers = getCurrentTopPlayers(10);
        
        if (topPlayers.length === 0) {
            return interaction.editReply({
                content: '📊 لا يوجد لاعبين في الموسم الحالي بعد.'
            });
        }
        
        // إنشاء embed للترتيب
        const embed = new EmbedBuilder()
            .setTitle(`🏅 ترتيب الموسم ${seasonNumber}`)
            .setColor(0xf1c40f)
            .setDescription('أفضل اللاعبين في الموسم الحالي')
            .setTimestamp();
        
        let leaderboardText = '';
        for (let i = 0; i < topPlayers.length; i++) {
            const player = topPlayers[i];
            const medal = ['🥇', '🥈', '🥉'][i] || `${i + 1}️⃣`;
            
            leaderboardText += `${medal} <@${player.userId}>\n`;
            leaderboardText += `   🎉 ${player.booyahPoints} بوياه | 💀 ${player.tkfatPoints} تكفات\n`;
            leaderboardText += `   📊 المجموع: ${player.totalPoints} | النسبة: ${player.ratio}\n\n`;
        }
        
        embed.setDescription(leaderboardText);
        
        await interaction.editReply({
            embeds: [embed]
        });
    } catch (error) {
        logger.error('خطأ في معالجة زر ترتيب الموسم:', error);
        await interaction.editReply({
            content: `⚠️ حدث خطأ: ${error.message}`
        });
    }
}

/**
 * معالجة زر إحصائيات الموسم
 * @param {Object} interaction - كائن التفاعل
 */
async function handleSeasonStatsButton(interaction) {
    try {
        await interaction.deferReply({ ephemeral: true });
        
        const seasonNumber = interaction.customId.split('_')[2];
        const { calculateCurrentSeasonStats } = require('../../seasons/seasons');
        const stats = calculateCurrentSeasonStats();
        
        // إنشاء embed للإحصائيات
        const embed = new EmbedBuilder()
            .setTitle(`📊 إحصائيات الموسم ${seasonNumber}`)
            .setColor(0x3498db)
            .setDescription('إحصائيات مفصلة للموسم الحالي')
            .addFields(
                {
                    name: '👥 إحصائيات اللاعبين',
                    value: `**إجمالي اللاعبين:** ${stats.players.total}\n` +
                           `**لاعبي البوياه:** ${stats.players.booyahPlayers}\n` +
                           `**لاعبي التكفات:** ${stats.players.tkfatPlayers}`,
                    inline: true
                },
                {
                    name: '💎 إحصائيات النقاط',
                    value: `**إجمالي البوياه:** ${stats.points.totalBooyah}\n` +
                           `**إجمالي التكفات:** ${stats.points.totalTkfat}\n` +
                           `**متوسط البوياه:** ${stats.points.avgBooyah}\n` +
                           `**متوسط التكفات:** ${stats.points.avgTkfat}`,
                    inline: true
                },
                {
                    name: '🏆 إحصائيات البطولات',
                    value: `**البطولات المكتملة:** ${stats.tournaments.completed}\n` +
                           `**البطولات النشطة:** ${stats.tournaments.active}\n` +
                           `**إجمالي البطولات:** ${stats.tournaments.total}`,
                    inline: true
                }
            )
            .setTimestamp();
        
        await interaction.editReply({
            embeds: [embed]
        });
    } catch (error) {
        logger.error('خطأ في معالجة زر إحصائيات الموسم:', error);
        await interaction.editReply({
            content: `⚠️ حدث خطأ: ${error.message}`
        });
    }
}

/**
 * معالجة زر تاريخ المواسم
 * @param {Object} interaction - كائن التفاعل
 */
async function handleSeasonHistoryButton(interaction) {
    try {
        await interaction.deferReply({ ephemeral: true });
        
        const result = getSeasonsHistory(10);
        
        if (!result.success) {
            return interaction.editReply({
                content: `⚠️ ${result.message}`
            });
        }
        
        const embed = createSeasonsHistoryEmbed(result.seasons, result.currentSeason);
        
        await interaction.editReply({
            embeds: [embed]
        });
    } catch (error) {
        logger.error('خطأ في معالجة زر تاريخ المواسم:', error);
        await interaction.editReply({
            content: `⚠️ حدث خطأ: ${error.message}`
        });
    }
}

/**
 * معالجة زر مكافآت الموسم
 * @param {Object} interaction - كائن التفاعل
 */
async function handleSeasonRewardsButton(interaction) {
    try {
        await interaction.deferReply({ ephemeral: true });
        
        const seasonNumber = interaction.customId.split('_')[2];
        
        // إنشاء بيانات المكافآت (يمكن تخصيصها لاحقاً)
        const rewardsData = {
            topRewards: [
                { position: 1, description: 'رتبة خاصة + 100 نقطة إضافية + شارة الفائز الأول' },
                { position: 2, description: 'رتبة خاصة + 50 نقطة إضافية + شارة الفائز الثاني' },
                { position: 3, description: 'رتبة خاصة + 25 نقطة إضافية + شارة الفائز الثالث' }
            ],
            specialRewards: [
                { emoji: '🔥', title: 'أكثر نشاطاً', description: 'للاعب الأكثر مشاركة في البطولات' },
                { emoji: '⚡', title: 'الصاعد الجديد', description: 'للاعب الجديد الأكثر تميزاً' },
                { emoji: '🎯', title: 'الأكثر انتظاماً', description: 'للاعب الأكثر انتظاماً في المشاركة' }
            ],
            participationRewards: {
                description: 'جميع اللاعبين النشطين يحصلون على 10 نقاط إضافية + شارة المشاركة'
            }
        };
        
        const embed = createSeasonRewardsEmbed(rewardsData, seasonNumber);
        
        await interaction.editReply({
            embeds: [embed]
        });
    } catch (error) {
        logger.error('خطأ في معالجة زر مكافآت الموسم:', error);
        await interaction.editReply({
            content: `⚠️ حدث خطأ: ${error.message}`
        });
    }
}

/**
 * معالجة زر معاينة موسم جديد
 * @param {Object} interaction - كائن التفاعل
 */
async function handleNewSeasonPreviewButton(interaction) {
    try {
        await interaction.deferReply({ ephemeral: true });
        
        // التحقق من الصلاحيات
        const member = await interaction.guild.members.fetch(interaction.user.id).catch(() => null);
        const { ADMIN_ROLE_ID } = require('../../../config/config');
        const isAdmin = member && (member.permissions.has("ManageEvents") || member.roles.cache.has(ADMIN_ROLE_ID));
        
        if (!isAdmin) {
            return interaction.editReply({
                content: '⚠️ هذه الميزة متاحة للمشرفين فقط.'
            });
        }
        
        // إنشاء معاينة الموسم الجديد
        const { data } = require('../../core/database');
        const currentSeason = data.seasons.current;
        const newSeasonNumber = currentSeason + 1;
        
        const embed = new EmbedBuilder()
            .setTitle(`🚀 معاينة الموسم الجديد ${newSeasonNumber}`)
            .setColor(0xe74c3c)
            .setDescription('**تحذير:** هذا الإجراء سيؤثر على جميع البيانات!')
            .addFields(
                {
                    name: '📊 الموسم الحالي',
                    value: `**الرقم:** ${currentSeason}\n` +
                           `**اللاعبين:** ${Object.keys(data.scores).length + Object.keys(data.tkfatScores).length}\n` +
                           `**النقاط:** ${Object.values(data.scores).reduce((a, b) => a + b, 0) + Object.values(data.tkfatScores).reduce((a, b) => a + b, 0)}`,
                    inline: true
                },
                {
                    name: '🆕 الموسم الجديد',
                    value: `**الرقم:** ${newSeasonNumber}\n` +
                           `**إعادة تعيين النقاط:** نعم\n` +
                           `**الاحتفاظ بالإحصائيات:** نعم`,
                    inline: true
                },
                {
                    name: '⚠️ ما سيحدث',
                    value: '• حفظ الموسم الحالي في التاريخ\n' +
                           '• إعادة تعيين جميع النقاط\n' +
                           '• بدء موسم جديد\n' +
                           '• إرسال إشعار للجميع',
                    inline: false
                }
            )
            .setFooter({ text: 'استخدم /season new لبدء موسم جديد فعلياً' })
            .setTimestamp();
        
        const confirmButton = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('season_new_confirm')
                    .setLabel('تأكيد بدء الموسم الجديد')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('🚀')
            );
        
        await interaction.editReply({
            embeds: [embed],
            components: [confirmButton]
        });
    } catch (error) {
        logger.error('خطأ في معالجة زر معاينة الموسم الجديد:', error);
        await interaction.editReply({
            content: `⚠️ حدث خطأ: ${error.message}`
        });
    }
}

/**
 * معالجة زر توزيع المكافآت
 * @param {Object} interaction - كائن التفاعل
 */
async function handleDistributeRewardsButton(interaction) {
    try {
        await interaction.deferReply({ ephemeral: true });
        
        // التحقق من الصلاحيات
        const member = await interaction.guild.members.fetch(interaction.user.id).catch(() => null);
        const { ADMIN_ROLE_ID } = require('../../../config/config');
        const isAdmin = member && (member.permissions.has("ManageEvents") || member.roles.cache.has(ADMIN_ROLE_ID));
        
        if (!isAdmin) {
            return interaction.editReply({
                content: '⚠️ هذه الميزة متاحة للمشرفين فقط.'
            });
        }
        
        await interaction.editReply({
            content: '🎁 **نظام توزيع المكافآت**\n\n' +
                    'هذه الميزة قيد التطوير وستكون متاحة قريباً!\n\n' +
                    '**الميزات المخططة:**\n' +
                    '• توزيع تلقائي للمكافآت\n' +
                    '• رتب خاصة للفائزين\n' +
                    '• شارات إنجاز مخصصة\n' +
                    '• نقاط إضافية للمتميزين'
        });
    } catch (error) {
        logger.error('خطأ في معالجة زر توزيع المكافآت:', error);
        await interaction.editReply({
            content: `⚠️ حدث خطأ: ${error.message}`
        });
    }
}

/**
 * معالجة زر تحديث السيزون
 * @param {Object} interaction - كائن التفاعل
 */
async function handleSeasonRefreshButton(interaction) {
    try {
        await interaction.deferUpdate();
        
        // إعادة إنشاء لوحة التحكم
        const result = await createSeasonDashboard(interaction);
        
        if (!result.success) {
            return interaction.editReply({
                content: `⚠️ ${result.message}`,
                components: []
            });
        }
        
        await interaction.editReply({
            embeds: result.embeds,
            components: result.components
        });
        
        logger.info(`تم تحديث لوحة تحكم السيزون بواسطة ${interaction.user.tag}`);
    } catch (error) {
        logger.error('خطأ في معالجة زر تحديث السيزون:', error);
        
        try {
            await interaction.editReply({
                content: `⚠️ حدث خطأ أثناء التحديث: ${error.message}`,
                components: []
            });
        } catch (replyError) {
            logger.error('خطأ في الرد على التفاعل:', replyError);
        }
    }
}

// تصدير الدوال
module.exports = {
    handleSeasonButton
};
