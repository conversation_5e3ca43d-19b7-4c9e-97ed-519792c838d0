/**
 * أمر إزالة نقاط بوياه من لاعب
 */

const { SlashCommandBuilder } = require('discord.js');
const logger = require('../modules/core/logger');
const { removeBooyahPoints, removeTkfatPoints } = require('../modules/points/points');
const { ADMIN_ROLE_ID } = require('../config/config');

// إنشاء بيانات الأمر
const commandData = new SlashCommandBuilder()
    .setName('removepoints')
    .setDescription('إزالة نقاط من لاعب')
    .addUserOption(option =>
        option.setName('user')
            .setDescription('اللاعب المراد إزالة النقاط منه')
            .setRequired(true)
    )
    .addIntegerOption(option =>
        option.setName('points')
            .setDescription('عدد النقاط المراد إزالتها')
            .setRequired(true)
            .setMinValue(1)
            .setMaxValue(100)
    )
    .addStringOption(option =>
        option.setName('type')
            .setDescription('نوع النقاط')
            .setRequired(true)
            .addChoices(
                { name: '🎉 Booyah (انتصار)', value: 'booyah' },
                { name: '💀 Tkfat (هزيمة)', value: 'tkfat' }
            )
    )
    .addStringOption(option =>
        option.setName('reason')
            .setDescription('سبب إزالة النقاط')
            .setRequired(false)
    );

/**
 * تنفيذ أمر إزالة النقاط
 * @param {Object} interaction - كائن التفاعل
 */
async function execute(interaction) {
    try {
        // التحقق من صلاحيات المستخدم
        const member = await interaction.guild.members.fetch(interaction.user.id);
        if (!member.permissions.has("MANAGE_EVENTS") && !member.roles.cache.has(ADMIN_ROLE_ID)) {
            return interaction.reply({
                content: "⚠️ ليس لديك صلاحيات كافية لإزالة نقاط.",
                ephemeral: true
            });
        }

        // الحصول على معلومات الأمر
        const targetUser = interaction.options.getUser('user');
        const points = interaction.options.getInteger('points');
        const type = interaction.options.getString('type');
        const reason = interaction.options.getString('reason') || "إزالة يدوية";

        // تحديد نوع النقاط والرموز
        let result, pointTypeName, emoji, dmEmoji;

        if (type === 'booyah') {
            result = removeBooyahPoints(targetUser.id, points, `${reason} (بواسطة ${interaction.user.tag})`);
            pointTypeName = 'بوياه';
            emoji = '🎉';
            dmEmoji = '⚠️';
        } else if (type === 'tkfat') {
            result = removeTkfatPoints(targetUser.id, points, `${reason} (بواسطة ${interaction.user.tag})`);
            pointTypeName = 'تكفات';
            emoji = '💀';
            dmEmoji = '⚠️';
        } else {
            return interaction.reply({
                content: '⚠️ نوع النقاط غير صالح.',
                ephemeral: true
            });
        }

        if (!result.success) {
            return interaction.reply({
                content: `⚠️ ${result.message}`,
                ephemeral: true
            });
        }

        // إرسال رسالة تأكيد
        await interaction.reply({
            content: `✅ تم إزالة ${points} نقطة ${pointTypeName} من **${targetUser.username}**.\nالسبب: ${reason}\nالإجمالي الجديد: ${result.newTotal} نقطة.`
        });

        // إرسال إشعار للاعب
        try {
            await targetUser.send(`${dmEmoji} تم إزالة ${points} نقطة ${pointTypeName} من حسابك.\nالسبب: ${reason}\nالإجمالي الجديد: ${result.newTotal} نقطة.`);
        } catch (dmError) {
            // تجاهل الأخطاء في إرسال الرسائل الخاصة
        }

        logger.success(`تم إزالة ${points} نقطة ${pointTypeName} من ${targetUser.tag} بواسطة ${interaction.user.tag}. السبب: ${reason}`);
    } catch (error) {
        logger.error('خطأ في تنفيذ أمر إزالة النقاط:', error);

        await interaction.reply({
            content: `⚠️ حدث خطأ أثناء تنفيذ الأمر: ${error.message}`,
            ephemeral: true
        }).catch(console.error);
    }
}

// تصدير الدوال
module.exports = {
    data: commandData,
    execute
};
