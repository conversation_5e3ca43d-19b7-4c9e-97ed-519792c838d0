/**
 * أمر إضافة نقاط بوياه للاعب
 */

const { SlashCommandBuilder } = require('discord.js');
const logger = require('../modules/core/logger');
const { addBooyahPoints, addTkfatPoints } = require('../modules/points/points');
const { ADMIN_ROLE_ID } = require('../config/config');

// إنشاء بيانات الأمر
const commandData = new SlashCommandBuilder()
    .setName('addpoints')
    .setDescription('إضافة نقاط للاعب')
    .addUserOption(option =>
        option.setName('user')
            .setDescription('اللاعب المراد إضافة النقاط له')
            .setRequired(true)
    )
    .addIntegerOption(option =>
        option.setName('points')
            .setDescription('عدد النقاط المراد إضافتها')
            .setRequired(true)
            .setMinValue(1)
            .setMaxValue(100)
    )
    .addStringOption(option =>
        option.setName('type')
            .setDescription('نوع النقاط')
            .setRequired(true)
            .addChoices(
                { name: '🎉 Booyah (انتصار)', value: 'booyah' },
                { name: '💀 Tkfat (هزيمة)', value: 'tkfat' }
            )
    )
    .addStringOption(option =>
        option.setName('reason')
            .setDescription('سبب إضافة النقاط')
            .setRequired(false)
    );

/**
 * تنفيذ أمر إضافة النقاط
 * @param {Object} interaction - كائن التفاعل
 */
async function execute(interaction) {
    try {
        // التحقق من صلاحيات المستخدم
        const member = await interaction.guild.members.fetch(interaction.user.id);
        if (!member.permissions.has("MANAGE_EVENTS") && !member.roles.cache.has(ADMIN_ROLE_ID)) {
            return interaction.reply({
                content: "⚠️ ليس لديك صلاحيات كافية لإضافة نقاط.",
                ephemeral: true
            });
        }

        // الحصول على معلومات الأمر
        const targetUser = interaction.options.getUser('user');
        const points = interaction.options.getInteger('points');
        const type = interaction.options.getString('type');
        const reason = interaction.options.getString('reason') || "إضافة يدوية";

        // تحديد نوع النقاط والرموز
        let result, pointTypeName, emoji, dmEmoji;

        if (type === 'booyah') {
            result = addBooyahPoints(targetUser.id, points, `${reason} (بواسطة ${interaction.user.tag})`);
            pointTypeName = 'بوياه';
            emoji = '🎉';
            dmEmoji = '🎉';
        } else if (type === 'tkfat') {
            result = addTkfatPoints(targetUser.id, points, `${reason} (بواسطة ${interaction.user.tag})`);
            pointTypeName = 'تكفات';
            emoji = '💀';
            dmEmoji = '💀';
        } else {
            return interaction.reply({
                content: '⚠️ نوع النقاط غير صالح.',
                ephemeral: true
            });
        }

        if (!result.success) {
            return interaction.reply({
                content: `⚠️ ${result.message}`,
                ephemeral: true
            });
        }

        // إرسال رسالة تأكيد
        await interaction.reply({
            content: `${emoji} تم إضافة ${points} نقطة ${pointTypeName} لـ **${targetUser.username}**.\nالسبب: ${reason}\nالإجمالي الجديد: ${result.newTotal} نقطة.`
        });

        // إرسال إشعار للاعب
        try {
            await targetUser.send(`${dmEmoji} تم إضافة ${points} نقطة ${pointTypeName} لحسابك.\nالسبب: ${reason}\nالإجمالي الجديد: ${result.newTotal} نقطة.`);
        } catch (dmError) {
            // تجاهل الأخطاء في إرسال الرسائل الخاصة
        }

        logger.success(`تم إضافة ${points} نقطة ${pointTypeName} لـ ${targetUser.tag} بواسطة ${interaction.user.tag}. السبب: ${reason}`);
    } catch (error) {
        logger.error('خطأ في تنفيذ أمر إضافة النقاط:', error);

        await interaction.reply({
            content: `⚠️ حدث خطأ أثناء تنفيذ الأمر: ${error.message}`,
            ephemeral: true
        }).catch(console.error);
    }
}

// تصدير الدوال
module.exports = {
    data: commandData,
    execute
};
