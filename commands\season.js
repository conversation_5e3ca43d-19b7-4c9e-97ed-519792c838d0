/**
 * أمر السيزونات الموحد - يجمع جميع وظائف المواسم في أمر واحد
 */

const { SlashCommandBuilder } = require('discord.js');
const logger = require('../modules/core/logger');

// إنشاء بيانات الأمر الموحد
const commandData = new SlashCommandBuilder()
    .setName('season')
    .setDescription('إدارة المواسم - جميع وظائف المواسم في مكان واحد')
    
    // أمر فرعي: الموسم الحالي
    .addSubcommand(subcommand =>
        subcommand
            .setName('current')
            .setDescription('عرض معلومات الموسم الحالي مع لوحة تحكم تفاعلية')
    )
    
    // أمر فرعي: تاريخ المواسم
    .addSubcommand(subcommand =>
        subcommand
            .setName('history')
            .setDescription('عرض تاريخ المواسم السابقة')
            .addIntegerOption(option =>
                option.setName('limit')
                    .setDescription('عدد المواسم المراد عرضها')
                    .setRequired(false)
                    .setMinValue(1)
                    .setMaxValue(20)
            )
    )
    
    // أمر فرعي: إحصائيات موسم معين
    .addSubcommand(subcommand =>
        subcommand
            .setName('stats')
            .setDescription('عرض إحصائيات موسم معين')
            .addIntegerOption(option =>
                option.setName('season_number')
                    .setDescription('رقم الموسم (اتركه فارغًا للموسم الحالي)')
                    .setRequired(false)
                    .setMinValue(1)
            )
    )
    
    // أمر فرعي: بدء موسم جديد
    .addSubcommand(subcommand =>
        subcommand
            .setName('new')
            .setDescription('بدء موسم جديد (للمشرفين فقط)')
            .addBooleanOption(option =>
                option.setName('reset_scores')
                    .setDescription('إعادة تعيين النقاط')
                    .setRequired(false)
            )
            .addBooleanOption(option =>
                option.setName('keep_stats')
                    .setDescription('الاحتفاظ بالإحصائيات')
                    .setRequired(false)
            )
            .addStringOption(option =>
                option.setName('announcement')
                    .setDescription('رسالة الإعلان عن الموسم الجديد')
                    .setRequired(false)
                    .setMaxLength(500)
            )
    )
    
    // أمر فرعي: مكافآت نهاية الموسم
    .addSubcommand(subcommand =>
        subcommand
            .setName('rewards')
            .setDescription('عرض وتوزيع مكافآت نهاية الموسم')
            .addIntegerOption(option =>
                option.setName('season_number')
                    .setDescription('رقم الموسم (اتركه فارغًا للموسم الحالي)')
                    .setRequired(false)
                    .setMinValue(1)
            )
            .addBooleanOption(option =>
                option.setName('distribute')
                    .setDescription('توزيع المكافآت فعلياً (للمشرفين فقط)')
                    .setRequired(false)
            )
    )
    
    // أمر فرعي: ترتيب الموسم
    .addSubcommand(subcommand =>
        subcommand
            .setName('leaderboard')
            .setDescription('عرض ترتيب الموسم')
            .addIntegerOption(option =>
                option.setName('season_number')
                    .setDescription('رقم الموسم (اتركه فارغًا للموسم الحالي)')
                    .setRequired(false)
                    .setMinValue(1)
            )
            .addStringOption(option =>
                option.setName('type')
                    .setDescription('نوع الترتيب')
                    .setRequired(false)
                    .addChoices(
                        { name: '🎉 Booyah (انتصارات)', value: 'booyah' },
                        { name: '💀 Tkfat (هزائم)', value: 'tkfat' },
                        { name: '📊 مجموع النقاط', value: 'total' },
                        { name: '🏆 البطولات', value: 'tournaments' }
                    )
            )
    );

/**
 * تنفيذ الأمر الموحد للمواسم
 * @param {Object} interaction - كائن التفاعل
 */
async function execute(interaction) {
    try {
        const subcommand = interaction.options.getSubcommand();
        
        // توجيه الأمر إلى المعالج المناسب
        switch (subcommand) {
            case 'current':
                await handleCurrentSeason(interaction);
                break;
            case 'history':
                await handleSeasonHistory(interaction);
                break;
            case 'stats':
                await handleSeasonStats(interaction);
                break;
            case 'new':
                await handleNewSeason(interaction);
                break;
            case 'rewards':
                await handleSeasonRewards(interaction);
                break;
            case 'leaderboard':
                await handleSeasonLeaderboard(interaction);
                break;
            default:
                await interaction.reply({
                    content: '⚠️ أمر فرعي غير معروف.',
                    ephemeral: true
                });
        }
        
        logger.info(`تم تنفيذ الأمر season ${subcommand} بواسطة ${interaction.user.tag}`);
    } catch (error) {
        logger.error(`خطأ في تنفيذ الأمر season ${interaction.options.getSubcommand()}:`, error);
        
        const errorMessage = `⚠️ حدث خطأ أثناء تنفيذ الأمر: ${error.message}`;
        
        if (interaction.deferred || interaction.replied) {
            await interaction.editReply({ content: errorMessage }).catch(console.error);
        } else {
            await interaction.reply({ content: errorMessage, ephemeral: true }).catch(console.error);
        }
    }
}

/**
 * معالجة أمر الموسم الحالي
 * @param {Object} interaction - كائن التفاعل
 */
async function handleCurrentSeason(interaction) {
    try {
        await interaction.deferReply();
        
        // استيراد دالة لوحة التحكم المحسنة
        const { createSeasonDashboard } = require('../modules/ui/season-dashboard');
        const result = await createSeasonDashboard(interaction);
        
        if (!result.success) {
            return interaction.editReply({
                content: `⚠️ ${result.message}`
            });
        }
        
        await interaction.editReply({
            embeds: result.embeds,
            components: result.components
        });
    } catch (error) {
        logger.error('خطأ في معالجة الموسم الحالي:', error);
        
        await interaction.editReply({
            content: `⚠️ حدث خطأ: ${error.message}`
        });
    }
}

/**
 * معالجة أمر تاريخ المواسم
 * @param {Object} interaction - كائن التفاعل
 */
async function handleSeasonHistory(interaction) {
    try {
        await interaction.deferReply();
        
        const limit = interaction.options.getInteger('limit') || 10;
        
        // استيراد دالة تاريخ المواسم
        const { getSeasonsHistory } = require('../modules/seasons/seasons');
        const result = getSeasonsHistory(limit);
        
        if (!result.success) {
            return interaction.editReply({
                content: `⚠️ ${result.message}`
            });
        }
        
        // إنشاء embed لتاريخ المواسم
        const { createSeasonsHistoryEmbed } = require('../modules/ui/season-embeds');
        const embed = createSeasonsHistoryEmbed(result.seasons, result.currentSeason);
        
        await interaction.editReply({
            embeds: [embed]
        });
    } catch (error) {
        logger.error('خطأ في معالجة تاريخ المواسم:', error);
        
        await interaction.editReply({
            content: `⚠️ حدث خطأ: ${error.message}`
        });
    }
}

/**
 * معالجة أمر إحصائيات الموسم
 * @param {Object} interaction - كائن التفاعل
 */
async function handleSeasonStats(interaction) {
    await interaction.reply({
        content: '⚠️ هذه الميزة قيد التطوير. ستكون متاحة قريباً!',
        ephemeral: true
    });
}

/**
 * معالجة أمر بدء موسم جديد
 * @param {Object} interaction - كائن التفاعل
 */
async function handleNewSeason(interaction) {
    await interaction.reply({
        content: '⚠️ هذه الميزة قيد التطوير. ستكون متاحة قريباً!',
        ephemeral: true
    });
}

/**
 * معالجة أمر مكافآت الموسم
 * @param {Object} interaction - كائن التفاعل
 */
async function handleSeasonRewards(interaction) {
    await interaction.reply({
        content: '⚠️ هذه الميزة قيد التطوير. ستكون متاحة قريباً!',
        ephemeral: true
    });
}

/**
 * معالجة أمر ترتيب الموسم
 * @param {Object} interaction - كائن التفاعل
 */
async function handleSeasonLeaderboard(interaction) {
    await interaction.reply({
        content: '⚠️ هذه الميزة قيد التطوير. ستكون متاحة قريباً!',
        ephemeral: true
    });
}

// تصدير الأمر
module.exports = {
    data: commandData,
    execute
};
