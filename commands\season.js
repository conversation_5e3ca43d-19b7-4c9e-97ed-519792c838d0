/**
 * أمر السيزونات الموحد - يجمع جميع وظائف المواسم في أمر واحد
 */

const { SlashCommandBuilder } = require('discord.js');
const logger = require('../modules/core/logger');

// إنشاء بيانات الأمر الموحد
const commandData = new SlashCommandBuilder()
    .setName('season')
    .setDescription('إدارة المواسم - جميع وظائف المواسم في مكان واحد')

    // أمر فرعي: الموسم الحالي
    .addSubcommand(subcommand =>
        subcommand
            .setName('current')
            .setDescription('عرض معلومات الموسم الحالي مع لوحة تحكم تفاعلية')
    )

    // أمر فرعي: تاريخ المواسم
    .addSubcommand(subcommand =>
        subcommand
            .setName('history')
            .setDescription('عرض تاريخ المواسم السابقة')
            .addIntegerOption(option =>
                option.setName('limit')
                    .setDescription('عدد المواسم المراد عرضها')
                    .setRequired(false)
                    .setMinValue(1)
                    .setMaxValue(20)
            )
    )

    // أمر فرعي: إحصائيات موسم معين
    .addSubcommand(subcommand =>
        subcommand
            .setName('stats')
            .setDescription('عرض إحصائيات موسم معين')
            .addIntegerOption(option =>
                option.setName('season_number')
                    .setDescription('رقم الموسم (اتركه فارغًا للموسم الحالي)')
                    .setRequired(false)
                    .setMinValue(1)
            )
    )

    // أمر فرعي: بدء موسم جديد
    .addSubcommand(subcommand =>
        subcommand
            .setName('new')
            .setDescription('بدء موسم جديد (للمشرفين فقط)')
            .addBooleanOption(option =>
                option.setName('reset_scores')
                    .setDescription('إعادة تعيين النقاط')
                    .setRequired(false)
            )
            .addBooleanOption(option =>
                option.setName('keep_stats')
                    .setDescription('الاحتفاظ بالإحصائيات')
                    .setRequired(false)
            )
            .addStringOption(option =>
                option.setName('announcement')
                    .setDescription('رسالة الإعلان عن الموسم الجديد')
                    .setRequired(false)
                    .setMaxLength(500)
            )
    )

    // أمر فرعي: مكافآت نهاية الموسم
    .addSubcommand(subcommand =>
        subcommand
            .setName('rewards')
            .setDescription('عرض وتوزيع مكافآت نهاية الموسم')
            .addIntegerOption(option =>
                option.setName('season_number')
                    .setDescription('رقم الموسم (اتركه فارغًا للموسم الحالي)')
                    .setRequired(false)
                    .setMinValue(1)
            )
            .addBooleanOption(option =>
                option.setName('distribute')
                    .setDescription('توزيع المكافآت فعلياً (للمشرفين فقط)')
                    .setRequired(false)
            )
    )

    // أمر فرعي: ترتيب الموسم
    .addSubcommand(subcommand =>
        subcommand
            .setName('leaderboard')
            .setDescription('عرض ترتيب الموسم')
            .addIntegerOption(option =>
                option.setName('season_number')
                    .setDescription('رقم الموسم (اتركه فارغًا للموسم الحالي)')
                    .setRequired(false)
                    .setMinValue(1)
            )
            .addStringOption(option =>
                option.setName('type')
                    .setDescription('نوع الترتيب')
                    .setRequired(false)
                    .addChoices(
                        { name: '🎉 Booyah (انتصارات)', value: 'booyah' },
                        { name: '💀 Tkfat (هزائم)', value: 'tkfat' },
                        { name: '📊 مجموع النقاط', value: 'total' },
                        { name: '🏆 البطولات', value: 'tournaments' }
                    )
            )
    );

/**
 * تنفيذ الأمر الموحد للمواسم
 * @param {Object} interaction - كائن التفاعل
 */
async function execute(interaction) {
    try {
        const subcommand = interaction.options.getSubcommand();

        // توجيه الأمر إلى المعالج المناسب
        switch (subcommand) {
            case 'current':
                await handleCurrentSeason(interaction);
                break;
            case 'history':
                await handleSeasonHistory(interaction);
                break;
            case 'stats':
                await handleSeasonStats(interaction);
                break;
            case 'new':
                await handleNewSeason(interaction);
                break;
            case 'rewards':
                await handleSeasonRewards(interaction);
                break;
            case 'leaderboard':
                await handleSeasonLeaderboard(interaction);
                break;
            default:
                await interaction.reply({
                    content: '⚠️ أمر فرعي غير معروف.',
                    ephemeral: true
                });
        }

        logger.info(`تم تنفيذ الأمر season ${subcommand} بواسطة ${interaction.user.tag}`);
    } catch (error) {
        logger.error(`خطأ في تنفيذ الأمر season ${interaction.options.getSubcommand()}:`, error);

        const errorMessage = `⚠️ حدث خطأ أثناء تنفيذ الأمر: ${error.message}`;

        if (interaction.deferred || interaction.replied) {
            await interaction.editReply({ content: errorMessage }).catch(console.error);
        } else {
            await interaction.reply({ content: errorMessage, ephemeral: true }).catch(console.error);
        }
    }
}

/**
 * معالجة أمر الموسم الحالي
 * @param {Object} interaction - كائن التفاعل
 */
async function handleCurrentSeason(interaction) {
    try {
        await interaction.deferReply();

        // استيراد دالة لوحة التحكم المحسنة
        const { createSeasonDashboard } = require('../modules/ui/season-dashboard');
        const result = await createSeasonDashboard(interaction);

        if (!result.success) {
            return interaction.editReply({
                content: `⚠️ ${result.message}`
            });
        }

        await interaction.editReply({
            embeds: result.embeds,
            components: result.components
        });
    } catch (error) {
        logger.error('خطأ في معالجة الموسم الحالي:', error);

        await interaction.editReply({
            content: `⚠️ حدث خطأ: ${error.message}`
        });
    }
}

/**
 * معالجة أمر تاريخ المواسم
 * @param {Object} interaction - كائن التفاعل
 */
async function handleSeasonHistory(interaction) {
    try {
        await interaction.deferReply();

        const limit = interaction.options.getInteger('limit') || 10;

        // استيراد دالة تاريخ المواسم
        const { getSeasonsHistory } = require('../modules/seasons/seasons');
        const result = getSeasonsHistory(limit);

        if (!result.success) {
            return interaction.editReply({
                content: `⚠️ ${result.message}`
            });
        }

        // إنشاء embed لتاريخ المواسم
        const { createSeasonsHistoryEmbed } = require('../modules/ui/season-embeds');
        const embed = createSeasonsHistoryEmbed(result.seasons, result.currentSeason);

        await interaction.editReply({
            embeds: [embed]
        });
    } catch (error) {
        logger.error('خطأ في معالجة تاريخ المواسم:', error);

        await interaction.editReply({
            content: `⚠️ حدث خطأ: ${error.message}`
        });
    }
}

/**
 * معالجة أمر إحصائيات الموسم
 * @param {Object} interaction - كائن التفاعل
 */
async function handleSeasonStats(interaction) {
    try {
        await interaction.deferReply();

        const seasonNumber = interaction.options.getInteger('season_number');
        const targetSeason = seasonNumber || require('../modules/core/database').data.seasons.current;

        // الحصول على إحصائيات الموسم
        const { calculateCurrentSeasonStats } = require('../modules/seasons/seasons');
        const stats = calculateCurrentSeasonStats();

        // إنشاء embed للإحصائيات المفصلة
        const { createSeasonStatsEmbed } = require('../modules/ui/season-embeds');

        // إعداد بيانات الموسم للعرض
        const seasonData = {
            startDate: require('../modules/core/database').data.seasons.startDate,
            endDate: null, // الموسم الحالي
            duration: Math.floor((new Date() - new Date(require('../modules/core/database').data.seasons.startDate)) / (1000 * 60 * 60 * 24)),
            playerStats: {
                total: stats.players.total,
                active: stats.players.total,
                new: Math.floor(stats.players.total * 0.3) // تقدير
            },
            pointsStats: {
                totalBooyah: stats.points.totalBooyah,
                totalTkfat: stats.points.totalTkfat
            },
            tournamentStats: {
                completed: stats.tournaments.completed,
                totalMatches: stats.tournaments.completed * 5, // تقدير
                avgParticipants: Math.floor(stats.players.total * 0.6) // تقدير
            },
            topPlayers: require('../modules/seasons/seasons').getCurrentTopPlayers(5)
        };

        const embed = createSeasonStatsEmbed(seasonData, targetSeason);

        await interaction.editReply({
            embeds: [embed]
        });
    } catch (error) {
        logger.error('خطأ في معالجة إحصائيات الموسم:', error);

        await interaction.editReply({
            content: `⚠️ حدث خطأ: ${error.message}`
        });
    }
}

/**
 * معالجة أمر بدء موسم جديد
 * @param {Object} interaction - كائن التفاعل
 */
async function handleNewSeason(interaction) {
    try {
        // التحقق من الصلاحيات
        const member = await interaction.guild.members.fetch(interaction.user.id).catch(() => null);
        const { ADMIN_ROLE_ID } = require('../config/config');
        const isAdmin = member && (member.permissions.has("ManageEvents") || member.roles.cache.has(ADMIN_ROLE_ID));

        if (!isAdmin) {
            return interaction.reply({
                content: '⚠️ هذا الأمر متاح للمشرفين فقط.',
                ephemeral: true
            });
        }

        await interaction.deferReply();

        // الحصول على الخيارات
        const resetScores = interaction.options.getBoolean('reset_scores') ?? true;
        const keepStats = interaction.options.getBoolean('keep_stats') ?? true;
        const announcement = interaction.options.getString('announcement');

        // بدء موسم جديد
        const { startNewSeason } = require('../modules/seasons/seasons');
        const result = startNewSeason({
            resetScores,
            keepStats,
            createdBy: interaction.user.id
        });

        if (!result.success) {
            return interaction.editReply({
                content: `⚠️ ${result.message}`
            });
        }

        // إنشاء embed للإعلان
        const { EmbedBuilder } = require('discord.js');
        const embed = new EmbedBuilder()
            .setTitle(`🚀 الموسم الجديد ${result.newSeason.number}`)
            .setColor(0x00ff00)
            .setDescription(announcement || `تم بدء الموسم الجديد رقم ${result.newSeason.number} بنجاح!`)
            .addFields(
                {
                    name: '📊 الموسم السابق',
                    value: `**الرقم:** ${result.previousSeason.number}\n` +
                           `**المدة:** ${Math.floor((new Date(result.previousSeason.endDate) - new Date(result.previousSeason.startDate)) / (1000 * 60 * 60 * 24))} يوم`,
                    inline: true
                },
                {
                    name: '🆕 الموسم الجديد',
                    value: `**الرقم:** ${result.newSeason.number}\n` +
                           `**إعادة تعيين النقاط:** ${resetScores ? 'نعم' : 'لا'}\n` +
                           `**الاحتفاظ بالإحصائيات:** ${keepStats ? 'نعم' : 'لا'}`,
                    inline: true
                }
            )
            .setFooter({
                text: `بدأ بواسطة ${interaction.user.tag}`,
                iconURL: interaction.user.displayAvatarURL()
            })
            .setTimestamp();

        await interaction.editReply({
            content: `🎉 **تم بدء الموسم الجديد ${result.newSeason.number} بنجاح!**`,
            embeds: [embed]
        });

        // إرسال إشعار عام (إذا كان هناك قناة إعلانات)
        try {
            const { ANNOUNCEMENT_CHANNEL_ID } = require('../config/config');
            if (ANNOUNCEMENT_CHANNEL_ID) {
                const announcementChannel = interaction.guild.channels.cache.get(ANNOUNCEMENT_CHANNEL_ID);
                if (announcementChannel) {
                    await announcementChannel.send({
                        content: '@everyone',
                        embeds: [embed]
                    });
                }
            }
        } catch (announcementError) {
            logger.warn('لم يتم إرسال الإعلان العام:', announcementError.message);
        }

        logger.success(`تم بدء الموسم الجديد ${result.newSeason.number} بواسطة ${interaction.user.tag}`);
    } catch (error) {
        logger.error('خطأ في معالجة أمر بدء موسم جديد:', error);

        if (interaction.deferred) {
            await interaction.editReply({
                content: `⚠️ حدث خطأ: ${error.message}`
            });
        } else {
            await interaction.reply({
                content: `⚠️ حدث خطأ: ${error.message}`,
                ephemeral: true
            });
        }
    }
}

/**
 * معالجة أمر مكافآت الموسم
 * @param {Object} interaction - كائن التفاعل
 */
async function handleSeasonRewards(interaction) {
    try {
        await interaction.deferReply();

        const seasonNumber = interaction.options.getInteger('season_number');
        const distribute = interaction.options.getBoolean('distribute') || false;
        const targetSeason = seasonNumber || require('../modules/core/database').data.seasons.current;

        // التحقق من الصلاحيات للتوزيع
        if (distribute) {
            const member = await interaction.guild.members.fetch(interaction.user.id).catch(() => null);
            const { ADMIN_ROLE_ID } = require('../config/config');
            const isAdmin = member && (member.permissions.has("ManageEvents") || member.roles.cache.has(ADMIN_ROLE_ID));

            if (!isAdmin) {
                return interaction.editReply({
                    content: '⚠️ توزيع المكافآت متاح للمشرفين فقط.'
                });
            }
        }

        // إنشاء بيانات المكافآت
        const rewardsData = {
            topRewards: [
                { position: 1, description: 'رتبة خاصة + 100 نقطة إضافية + شارة الفائز الأول 🥇' },
                { position: 2, description: 'رتبة خاصة + 50 نقطة إضافية + شارة الفائز الثاني 🥈' },
                { position: 3, description: 'رتبة خاصة + 25 نقطة إضافية + شارة الفائز الثالث 🥉' }
            ],
            specialRewards: [
                { emoji: '🔥', title: 'أكثر نشاطاً', description: 'للاعب الأكثر مشاركة في البطولات' },
                { emoji: '⚡', title: 'الصاعد الجديد', description: 'للاعب الجديد الأكثر تميزاً' },
                { emoji: '🎯', title: 'الأكثر انتظاماً', description: 'للاعب الأكثر انتظاماً في المشاركة' }
            ],
            participationRewards: {
                description: 'جميع اللاعبين النشطين يحصلون على 10 نقاط إضافية + شارة المشاركة'
            }
        };

        const { createSeasonRewardsEmbed } = require('../modules/ui/season-embeds');
        const embed = createSeasonRewardsEmbed(rewardsData, targetSeason);

        if (distribute) {
            // هنا يمكن إضافة منطق توزيع المكافآت الفعلي
            embed.setFooter({ text: '🎁 تم توزيع المكافآت بنجاح!' });

            await interaction.editReply({
                content: '🎉 **تم توزيع مكافآت الموسم بنجاح!**',
                embeds: [embed]
            });
        } else {
            await interaction.editReply({
                embeds: [embed]
            });
        }
    } catch (error) {
        logger.error('خطأ في معالجة أمر مكافآت الموسم:', error);

        await interaction.editReply({
            content: `⚠️ حدث خطأ: ${error.message}`
        });
    }
}

/**
 * معالجة أمر ترتيب الموسم
 * @param {Object} interaction - كائن التفاعل
 */
async function handleSeasonLeaderboard(interaction) {
    try {
        await interaction.deferReply();

        const seasonNumber = interaction.options.getInteger('season_number');
        const type = interaction.options.getString('type') || 'total';
        const targetSeason = seasonNumber || require('../modules/core/database').data.seasons.current;

        // الحصول على أفضل اللاعبين
        const { getCurrentTopPlayers } = require('../modules/seasons/seasons');
        const topPlayers = getCurrentTopPlayers(15);

        if (topPlayers.length === 0) {
            return interaction.editReply({
                content: '📊 لا يوجد لاعبين في الموسم الحالي بعد.'
            });
        }

        // ترتيب حسب النوع المطلوب
        let sortedPlayers = [...topPlayers];
        let title = '';
        let description = '';

        switch (type) {
            case 'booyah':
                sortedPlayers.sort((a, b) => b.booyahPoints - a.booyahPoints);
                title = `🎉 ترتيب البوياه - الموسم ${targetSeason}`;
                description = 'أفضل اللاعبين في نقاط البوياه (الانتصارات)';
                break;
            case 'tkfat':
                sortedPlayers.sort((a, b) => b.tkfatPoints - a.tkfatPoints);
                title = `💀 ترتيب التكفات - الموسم ${targetSeason}`;
                description = 'أكثر اللاعبين في نقاط التكفات (الهزائم)';
                break;
            case 'tournaments':
                title = `🏆 ترتيب البطولات - الموسم ${targetSeason}`;
                description = 'أفضل اللاعبين في البطولات (قيد التطوير)';
                break;
            default: // total
                title = `📊 الترتيب العام - الموسم ${targetSeason}`;
                description = 'أفضل اللاعبين حسب إجمالي النقاط';
        }

        // إنشاء embed للترتيب
        const { EmbedBuilder } = require('discord.js');
        const embed = new EmbedBuilder()
            .setTitle(title)
            .setColor(0xf1c40f)
            .setDescription(description)
            .setTimestamp();

        let leaderboardText = '';
        for (let i = 0; i < Math.min(10, sortedPlayers.length); i++) {
            const player = sortedPlayers[i];
            const medal = ['🥇', '🥈', '🥉'][i] || `**${i + 1}.**`;

            let pointsText = '';
            switch (type) {
                case 'booyah':
                    pointsText = `${player.booyahPoints} بوياه`;
                    break;
                case 'tkfat':
                    pointsText = `${player.tkfatPoints} تكفات`;
                    break;
                default:
                    pointsText = `${player.totalPoints} نقطة (${player.booyahPoints} بوياه، ${player.tkfatPoints} تكفات)`;
            }

            leaderboardText += `${medal} <@${player.userId}> - ${pointsText}\n`;
        }

        embed.addFields({
            name: `أفضل ${Math.min(10, sortedPlayers.length)} لاعب`,
            value: leaderboardText,
            inline: false
        });

        // إضافة إحصائيات إضافية
        const totalPlayers = sortedPlayers.length;
        const totalPoints = sortedPlayers.reduce((sum, p) => sum + p.totalPoints, 0);
        const avgPoints = totalPlayers > 0 ? Math.round(totalPoints / totalPlayers) : 0;

        embed.addFields({
            name: '📈 إحصائيات عامة',
            value: `**إجمالي اللاعبين:** ${totalPlayers}\n` +
                   `**إجمالي النقاط:** ${totalPoints}\n` +
                   `**متوسط النقاط:** ${avgPoints}`,
            inline: true
        });

        await interaction.editReply({
            embeds: [embed]
        });
    } catch (error) {
        logger.error('خطأ في معالجة أمر ترتيب الموسم:', error);

        await interaction.editReply({
            content: `⚠️ حدث خطأ: ${error.message}`
        });
    }
}

// تصدير الأمر
module.exports = {
    data: commandData,
    execute
};
