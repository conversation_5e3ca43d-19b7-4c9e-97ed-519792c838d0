=== بدء تشغيل البوت 2025-05-22T05:36:24.954Z ===

[2025-05-22T05:36:24.959Z] [SUCCESS] تم تحميل جميع البيانات بنجاح
[2025-05-22T05:36:25.072Z] [INFO] جاري تسجيل أوامر السلاش...
[2025-05-22T05:36:25.778Z] [SUCCESS] تم تسجيل أوامر السلاش بنجاح
[2025-05-22T05:36:26.456Z] [SUCCESS] ✅ تم تسجيل الدخول باسم Manga FF#9245
[2025-05-22T05:36:26.457Z] [INFO] 🤖 البوت متصل بـ 1 سيرفر
[2025-05-22T05:36:26.457Z] [INFO] 🏆 الموسم الحالي: 1
[2025-05-22T05:36:26.458Z] [INFO] تم تحميل الأمر: addpoints
[2025-05-22T05:36:26.459Z] [INFO] تم تحميل الأمر: backup
[2025-05-22T05:36:26.460Z] [INFO] تم تحميل الأمر: booyah
[2025-05-22T05:36:26.461Z] [INFO] تم تحميل الأمر: help
[2025-05-22T05:36:26.461Z] [INFO] تم تحميل الأمر: new-season
[2025-05-22T05:36:26.462Z] [INFO] تم تحميل الأمر: notifications
[2025-05-22T05:36:26.462Z] [INFO] تم تحميل الأمر: record-match-ui
[2025-05-22T05:36:26.463Z] [INFO] تم تحميل الأمر: record-match
[2025-05-22T05:36:26.463Z] [INFO] تم تحميل الأمر: removepoints
[2025-05-22T05:36:26.464Z] [INFO] تم تحميل الأمر: statistics
[2025-05-22T05:36:26.464Z] [INFO] تم تحميل الأمر: stats
[2025-05-22T05:36:26.464Z] [INFO] تم تحميل الأمر: team-management
[2025-05-22T05:36:26.465Z] [INFO] تم تحميل الأمر: team
[2025-05-22T05:36:26.465Z] [INFO] تم تحميل الأمر: teams
[2025-05-22T05:36:26.466Z] [INFO] تم تحميل الأمر: test
[2025-05-22T05:36:26.466Z] [INFO] تم تحميل الأمر: tkfat
[2025-05-22T05:36:26.467Z] [INFO] تم تحميل الأمر: tournament-cancel
[2025-05-22T05:36:26.467Z] [INFO] تم تحميل الأمر: tournament-complete
[2025-05-22T05:36:26.468Z] [INFO] تم تحميل الأمر: tournament-create
[2025-05-22T05:36:26.468Z] [INFO] تم تحميل الأمر: tournament-dashboard
[2025-05-22T05:36:26.469Z] [INFO] تم تحميل الأمر: tournament-info
[2025-05-22T05:36:26.469Z] [INFO] تم تحميل الأمر: tournament-invite
[2025-05-22T05:36:26.469Z] [INFO] تم تحميل الأمر: tournament-leaderboard
[2025-05-22T05:36:26.470Z] [INFO] تم تحميل الأمر: tournament-match
[2025-05-22T05:36:26.470Z] [INFO] تم تحميل الأمر: tournament-notifications
[2025-05-22T05:36:26.471Z] [INFO] تم تحميل الأمر: tournament-register-ui
[2025-05-22T05:36:26.472Z] [INFO] تم تحميل الأمر: tournament-register
[2025-05-22T05:36:26.472Z] [INFO] تم تحميل الأمر: tournament-start
[2025-05-22T05:36:26.472Z] [INFO] تم تحميل الأمر: tournament
[2025-05-22T05:36:26.473Z] [SUCCESS] تم تحميل 29 أمر بنجاح
[2025-05-22T05:36:26.473Z] [SUCCESS] ✅ تم تسجيل الدخول باسم Manga FF#9245
[2025-05-22T05:36:26.474Z] [INFO] 🤖 البوت متصل بـ 1 سيرفر
[2025-05-22T05:36:26.474Z] [INFO] 🏆 الموسم الحالي: 1
[2025-05-22T05:36:26.475Z] [INFO] تم تهيئة نظام إشعارات البطولات
[2025-05-22T05:36:26.476Z] [INFO] 📢 تم تهيئة نظام إشعارات البطولات
[2025-05-22T05:36:26.476Z] [INFO] 📅 إعادة جدولة 0 إشعار معلق
[2025-05-22T05:36:31.500Z] [SUCCESS] تم حفظ جميع البيانات بنجاح
[2025-05-22T05:36:33.401Z] [ERROR] خطأ في تنفيذ أمر الإحصائيات:
TypeError: interaction.deferReply is not a function
    at Object.execute (C:\Users\<USER>\Documents\augment-projects\free fire\commands\stats.js:27:27)
    at handleViewStats (C:\Users\<USER>\Documents\augment-projects\free fire\commands\statistics.js:176:27)
    at Object.execute (C:\Users\<USER>\Documents\augment-projects\free fire\commands\statistics.js:119:23)
    at Object.execute (C:\Users\<USER>\Documents\augment-projects\free fire\events\interactionCreate.js:48:35)
    at Client.<anonymous> (C:\Users\<USER>\Documents\augment-projects\free fire\index.js:53:50)
    at Client.emit (node:events:524:28)
    at InteractionCreateAction.handle (C:\Users\<USER>\Documents\augment-projects\free fire\node_modules\discord.js\src\client\actions\InteractionCreate.js:97:12)
    at module.exports [as INTERACTION_CREATE] (C:\Users\<USER>\Documents\augment-projects\free fire\node_modules\discord.js\src\client\websocket\handlers\INTERACTION_CREATE.js:4:36)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Documents\augment-projects\free fire\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Documents\augment-projects\free fire\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
[2025-05-22T05:36:33.402Z] [ERROR] خطأ في تنفيذ الأمر statistics view:
TypeError: interaction.reply is not a function
    at Object.execute (C:\Users\<USER>\Documents\augment-projects\free fire\commands\stats.js:75:31)
    at handleViewStats (C:\Users\<USER>\Documents\augment-projects\free fire\commands\statistics.js:176:27)
    at Object.execute (C:\Users\<USER>\Documents\augment-projects\free fire\commands\statistics.js:119:23)
    at Object.execute (C:\Users\<USER>\Documents\augment-projects\free fire\events\interactionCreate.js:48:35)
    at Client.<anonymous> (C:\Users\<USER>\Documents\augment-projects\free fire\index.js:53:50)
    at Client.emit (node:events:524:28)
    at InteractionCreateAction.handle (C:\Users\<USER>\Documents\augment-projects\free fire\node_modules\discord.js\src\client\actions\InteractionCreate.js:97:12)
    at module.exports [as INTERACTION_CREATE] (C:\Users\<USER>\Documents\augment-projects\free fire\node_modules\discord.js\src\client\websocket\handlers\INTERACTION_CREATE.js:4:36)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Documents\augment-projects\free fire\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Documents\augment-projects\free fire\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
[2025-05-22T05:36:33.924Z] [INFO] تم تنفيذ الأمر statistics بواسطة .z2r
[2025-05-22T05:36:52.374Z] [INFO] تم عرض لوحة تحكم البطولة 4 (التبويب: info) بواسطة .z2r
[2025-05-22T05:36:52.375Z] [INFO] تم تنفيذ الأمر tournament-dashboard بواسطة .z2r
[2025-05-22T05:37:00.705Z] [SUCCESS] تم إغلاق التسجيل في البطولة: test 4 (ID: 4)
[2025-05-22T05:37:02.022Z] [INFO] تم إغلاق التسجيل في البطولة 4 بواسطة .z2r
[2025-05-22T05:37:04.612Z] [SUCCESS] تم فتح التسجيل في البطولة: test 4 (ID: 4)
[2025-05-22T05:37:05.687Z] [INFO] تم فتح التسجيل في البطولة 4 بواسطة .z2r
[2025-05-22T05:37:09.628Z] [SUCCESS] تم حفظ جميع البيانات بنجاح
