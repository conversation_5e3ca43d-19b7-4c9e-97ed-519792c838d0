/**
 * وحدة embeds السيزونات
 * توفر embeds منسقة لعرض معلومات المواسم
 */

const { EmbedBuilder } = require('discord.js');

// الألوان والرموز
const COLORS = {
    SEASON: {
        current: 0x00ff00,
        completed: 0x3498db,
        history: 0x9b59b6,
        stats: 0xf39c12
    }
};

const EMOJIS = {
    SEASON: '🏆',
    CURRENT: '⭐',
    COMPLETED: '✅',
    DURATION: '⏱️',
    PLAYERS: '👥',
    TOURNAMENTS: '🏟️',
    POINTS: '💎',
    CROWN: '👑',
    CALENDAR: '📅',
    CHART: '📊'
};

/**
 * إنشاء embed لتاريخ المواسم
 * @param {Array} seasons - قائمة المواسم
 * @param {number} currentSeason - رقم الموسم الحالي
 * @returns {EmbedBuilder} - الرسالة المضمنة
 */
function createSeasonsHistoryEmbed(seasons, currentSeason) {
    const embed = new EmbedBuilder()
        .setTitle(`${EMOJIS.SEASON} تاريخ المواسم`)
        .setColor(COLORS.SEASON.history)
        .setDescription(`سجل المواسم السابقة والحالية (${seasons.length} موسم)`)
        .setTimestamp();

    // إضافة حقول المواسم
    for (const season of seasons.slice(0, 10)) { // عرض أول 10 مواسم فقط
        const isCurrentSeason = season.number === currentSeason;
        const statusEmoji = isCurrentSeason ? EMOJIS.CURRENT : EMOJIS.COMPLETED;
        const statusText = isCurrentSeason ? 'جاري' : 'مكتمل';
        
        let fieldValue = `**الحالة:** ${statusText} ${statusEmoji}\n`;
        fieldValue += `**البداية:** ${season.startDate}\n`;
        
        if (!isCurrentSeason && season.endDate) {
            fieldValue += `**النهاية:** ${season.endDate}\n`;
        }
        
        fieldValue += `**المدة:** ${season.duration} يوم\n`;
        
        // إضافة إحصائيات إضافية إذا كانت متوفرة
        if (season.topPlayers && season.topPlayers.length > 0) {
            fieldValue += `**أفضل لاعب:** <@${season.topPlayers[0].userId}> (${season.topPlayers[0].points} نقطة)`;
        }

        embed.addFields({
            name: `${EMOJIS.SEASON} الموسم ${season.number}`,
            value: fieldValue,
            inline: true
        });
    }

    // إضافة footer مع معلومات إضافية
    embed.setFooter({
        text: `الموسم الحالي: ${currentSeason} | إجمالي المواسم: ${seasons.length}`
    });

    return embed;
}

/**
 * إنشاء embed لإحصائيات موسم معين
 * @param {Object} seasonData - بيانات الموسم
 * @param {number} seasonNumber - رقم الموسم
 * @returns {EmbedBuilder} - الرسالة المضمنة
 */
function createSeasonStatsEmbed(seasonData, seasonNumber) {
    const embed = new EmbedBuilder()
        .setTitle(`${EMOJIS.CHART} إحصائيات الموسم ${seasonNumber}`)
        .setColor(COLORS.SEASON.stats)
        .setTimestamp();

    // الوصف الأساسي
    let description = `تفاصيل شاملة عن الموسم ${seasonNumber}\n\n`;
    
    if (seasonData.startDate) {
        description += `**${EMOJIS.CALENDAR} تاريخ البداية:** ${formatDate(seasonData.startDate)}\n`;
    }
    
    if (seasonData.endDate) {
        description += `**${EMOJIS.CALENDAR} تاريخ النهاية:** ${formatDate(seasonData.endDate)}\n`;
    }
    
    if (seasonData.duration) {
        description += `**${EMOJIS.DURATION} المدة:** ${seasonData.duration} يوم\n`;
    }

    embed.setDescription(description);

    // إحصائيات اللاعبين
    if (seasonData.playerStats) {
        embed.addFields({
            name: `${EMOJIS.PLAYERS} إحصائيات اللاعبين`,
            value: `**إجمالي اللاعبين:** ${seasonData.playerStats.total || 0}\n` +
                   `**اللاعبين النشطين:** ${seasonData.playerStats.active || 0}\n` +
                   `**لاعبين جدد:** ${seasonData.playerStats.new || 0}`,
            inline: true
        });
    }

    // إحصائيات النقاط
    if (seasonData.pointsStats) {
        embed.addFields({
            name: `${EMOJIS.POINTS} إحصائيات النقاط`,
            value: `**نقاط البوياه:** ${seasonData.pointsStats.totalBooyah || 0}\n` +
                   `**نقاط التكفات:** ${seasonData.pointsStats.totalTkfat || 0}\n` +
                   `**إجمالي النقاط:** ${(seasonData.pointsStats.totalBooyah || 0) + (seasonData.pointsStats.totalTkfat || 0)}`,
            inline: true
        });
    }

    // إحصائيات البطولات
    if (seasonData.tournamentStats) {
        embed.addFields({
            name: `${EMOJIS.TOURNAMENTS} إحصائيات البطولات`,
            value: `**البطولات المكتملة:** ${seasonData.tournamentStats.completed || 0}\n` +
                   `**إجمالي المباريات:** ${seasonData.tournamentStats.totalMatches || 0}\n` +
                   `**متوسط المشاركين:** ${seasonData.tournamentStats.avgParticipants || 0}`,
            inline: true
        });
    }

    // أفضل اللاعبين
    if (seasonData.topPlayers && seasonData.topPlayers.length > 0) {
        let topPlayersValue = '';
        for (let i = 0; i < Math.min(5, seasonData.topPlayers.length); i++) {
            const player = seasonData.topPlayers[i];
            const medal = ['🥇', '🥈', '🥉', '4️⃣', '5️⃣'][i];
            topPlayersValue += `${medal} <@${player.userId}> - ${player.points} نقطة\n`;
        }

        embed.addFields({
            name: `${EMOJIS.CROWN} أفضل اللاعبين`,
            value: topPlayersValue,
            inline: false
        });
    }

    return embed;
}

/**
 * إنشاء embed لمكافآت الموسم
 * @param {Object} rewardsData - بيانات المكافآت
 * @param {number} seasonNumber - رقم الموسم
 * @returns {EmbedBuilder} - الرسالة المضمنة
 */
function createSeasonRewardsEmbed(rewardsData, seasonNumber) {
    const embed = new EmbedBuilder()
        .setTitle(`🎁 مكافآت الموسم ${seasonNumber}`)
        .setColor(COLORS.SEASON.completed)
        .setDescription(`مكافآت نهاية الموسم للاعبين المتميزين`)
        .setTimestamp();

    // مكافآت المراكز الأولى
    if (rewardsData.topRewards && rewardsData.topRewards.length > 0) {
        let topRewardsValue = '';
        for (const reward of rewardsData.topRewards) {
            const medal = reward.position === 1 ? '🥇' : reward.position === 2 ? '🥈' : '🥉';
            topRewardsValue += `${medal} **المركز ${reward.position}:** ${reward.description}\n`;
        }

        embed.addFields({
            name: '🏆 مكافآت المراكز الأولى',
            value: topRewardsValue,
            inline: false
        });
    }

    // مكافآت خاصة
    if (rewardsData.specialRewards && rewardsData.specialRewards.length > 0) {
        let specialRewardsValue = '';
        for (const reward of rewardsData.specialRewards) {
            specialRewardsValue += `${reward.emoji} **${reward.title}:** ${reward.description}\n`;
        }

        embed.addFields({
            name: '⭐ مكافآت خاصة',
            value: specialRewardsValue,
            inline: false
        });
    }

    // مكافآت المشاركة
    if (rewardsData.participationRewards) {
        embed.addFields({
            name: '🎖️ مكافآت المشاركة',
            value: rewardsData.participationRewards.description || 'مكافآت لجميع المشاركين النشطين',
            inline: false
        });
    }

    return embed;
}

/**
 * تنسيق التاريخ
 * @param {string} dateString - التاريخ كنص
 * @returns {string} - التاريخ المنسق
 */
function formatDate(dateString) {
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    } catch (error) {
        return dateString;
    }
}

// تصدير الدوال
module.exports = {
    createSeasonsHistoryEmbed,
    createSeasonStatsEmbed,
    createSeasonRewardsEmbed
};
