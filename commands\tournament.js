/**
 * أمر البطولات الموحد - يجمع جميع وظائف البطولات في أمر واحد
 */

const { SlashCommandBuilder } = require('discord.js');
const logger = require('../modules/core/logger');

// استيراد الأوامر الأصلية المطلوبة
const { createTournamentDashboard } = require('../modules/ui/tournament-dashboard');
const { getActiveTournaments } = require('../modules/tournaments/tournament');

// إنشاء بيانات الأمر الموحد
const commandData = new SlashCommandBuilder()
    .setName('tournament')
    .setDescription('إدارة البطولات - جميع وظائف البطولات في مكان واحد')

    // أمر فرعي: إنشاء بطولة
    .addSubcommand(subcommand =>
        subcommand
            .setName('create')
            .setDescription('إنشاء بطولة جديدة')
            .addStringOption(option =>
                option.setName('name')
                    .setDescription('اسم البطولة')
                    .setRequired(true)
                    .setMaxLength(50)
            )
            .addStringOption(option =>
                option.setName('type')
                    .setDescription('نوع البطولة')
                    .setRequired(true)
                    .addChoices(
                        { name: '🥊 خروج المغلوب', value: 'knockout' },
                        { name: '🏆 دوري', value: 'league' },
                        { name: '👑 باتل رويال', value: 'battle_royale' }
                    )
            )
            .addIntegerOption(option =>
                option.setName('max_teams')
                    .setDescription('العدد الأقصى للفرق')
                    .setRequired(true)
                    .setMinValue(2)
                    .setMaxValue(64)
            )
            .addIntegerOption(option =>
                option.setName('players_per_team')
                    .setDescription('عدد اللاعبين في كل فريق')
                    .setRequired(true)
                    .setMinValue(1)
                    .setMaxValue(6)
            )
            .addStringOption(option =>
                option.setName('description')
                    .setDescription('وصف البطولة')
                    .setRequired(false)
                    .setMaxLength(200)
            )
    )

    // أمر فرعي: التسجيل في بطولة
    .addSubcommand(subcommand =>
        subcommand
            .setName('register')
            .setDescription('التسجيل في بطولة')
            .addIntegerOption(option =>
                option.setName('tournament_id')
                    .setDescription('معرف البطولة')
                    .setRequired(false)
            )
    )

    // أمر فرعي: بدء بطولة
    .addSubcommand(subcommand =>
        subcommand
            .setName('start')
            .setDescription('بدء بطولة')
            .addIntegerOption(option =>
                option.setName('tournament_id')
                    .setDescription('معرف البطولة')
                    .setRequired(true)
            )
    )

    // أمر فرعي: إدارة المباريات
    .addSubcommand(subcommand =>
        subcommand
            .setName('match')
            .setDescription('إدارة مباريات البطولة')
            .addIntegerOption(option =>
                option.setName('tournament_id')
                    .setDescription('معرف البطولة')
                    .setRequired(true)
            )
            .addIntegerOption(option =>
                option.setName('match_id')
                    .setDescription('معرف المباراة (اختياري)')
                    .setRequired(false)
            )
    )

    // أمر فرعي: إلغاء بطولة
    .addSubcommand(subcommand =>
        subcommand
            .setName('cancel')
            .setDescription('إلغاء بطولة')
            .addIntegerOption(option =>
                option.setName('tournament_id')
                    .setDescription('معرف البطولة')
                    .setRequired(true)
            )
            .addStringOption(option =>
                option.setName('reason')
                    .setDescription('سبب الإلغاء')
                    .setRequired(false)
            )
    )

    // أمر فرعي: لوحة التحكم
    .addSubcommand(subcommand =>
        subcommand
            .setName('dashboard')
            .setDescription('عرض لوحة تحكم البطولة')
            .addIntegerOption(option =>
                option.setName('tournament_id')
                    .setDescription('معرف البطولة (اتركه فارغًا لعرض البطولات النشطة)')
                    .setRequired(false)
            )
    )

    // أمر فرعي: الترتيب
    .addSubcommand(subcommand =>
        subcommand
            .setName('leaderboard')
            .setDescription('عرض ترتيب البطولة')
            .addIntegerOption(option =>
                option.setName('tournament_id')
                    .setDescription('معرف البطولة')
                    .setRequired(true)
            )
    )

    // أمر فرعي: معلومات البطولة
    .addSubcommand(subcommand =>
        subcommand
            .setName('info')
            .setDescription('عرض معلومات البطولة')
            .addIntegerOption(option =>
                option.setName('tournament_id')
                    .setDescription('معرف البطولة')
                    .setRequired(true)
            )
    );

/**
 * تنفيذ الأمر الموحد للبطولات
 * @param {Object} interaction - كائن التفاعل
 */
async function execute(interaction) {
    try {
        const subcommand = interaction.options.getSubcommand();

        // توجيه الأمر إلى المعالج المناسب
        switch (subcommand) {
            case 'create':
                await handleCreate(interaction);
                break;
            case 'register':
                await handleRegister(interaction);
                break;
            case 'start':
                await handleStart(interaction);
                break;
            case 'match':
                await handleMatch(interaction);
                break;
            case 'cancel':
                await handleCancel(interaction);
                break;
            case 'dashboard':
                await handleDashboard(interaction);
                break;
            case 'leaderboard':
                await handleLeaderboard(interaction);
                break;
            case 'info':
                await handleInfo(interaction);
                break;
            default:
                await interaction.reply({
                    content: '⚠️ أمر فرعي غير معروف.',
                    ephemeral: true
                });
        }

        logger.info(`تم تنفيذ الأمر tournament ${subcommand} بواسطة ${interaction.user.tag}`);
    } catch (error) {
        logger.error(`خطأ في تنفيذ الأمر tournament ${interaction.options.getSubcommand()}:`, error);

        const errorMessage = `⚠️ حدث خطأ أثناء تنفيذ الأمر: ${error.message}`;

        if (interaction.deferred || interaction.replied) {
            await interaction.editReply({ content: errorMessage }).catch(console.error);
        } else {
            await interaction.reply({ content: errorMessage, ephemeral: true }).catch(console.error);
        }
    }
}

/**
 * معالجة أمر إنشاء البطولة
 */
async function handleCreate(interaction) {
    await interaction.reply({
        content: '⚠️ هذه الميزة قيد التطوير. استخدم الأوامر الأصلية حاليًا.',
        ephemeral: true
    });
}

/**
 * معالجة أمر التسجيل في البطولة
 */
async function handleRegister(interaction) {
    await interaction.reply({
        content: '⚠️ هذه الميزة قيد التطوير. استخدم الأوامر الأصلية حاليًا.',
        ephemeral: true
    });
}

/**
 * معالجة أمر بدء البطولة
 */
async function handleStart(interaction) {
    await interaction.reply({
        content: '⚠️ هذه الميزة قيد التطوير. استخدم الأوامر الأصلية حاليًا.',
        ephemeral: true
    });
}

/**
 * معالجة أمر إدارة المباريات
 */
async function handleMatch(interaction) {
    await interaction.reply({
        content: '⚠️ هذه الميزة قيد التطوير. استخدم الأوامر الأصلية حاليًا.',
        ephemeral: true
    });
}

/**
 * معالجة أمر إلغاء البطولة
 */
async function handleCancel(interaction) {
    await interaction.reply({
        content: '⚠️ هذه الميزة قيد التطوير. استخدم الأوامر الأصلية حاليًا.',
        ephemeral: true
    });
}

/**
 * معالجة أمر لوحة التحكم
 */
async function handleDashboard(interaction) {
    try {
        await interaction.deferReply();

        const tournamentId = interaction.options.getInteger('tournament_id');

        if (tournamentId) {
            const result = await createTournamentDashboard(interaction, tournamentId);

            if (!result.success) {
                return interaction.editReply({
                    content: `⚠️ ${result.message}`
                });
            }

            await interaction.editReply({
                embeds: result.embeds,
                components: result.components
            });
        } else {
            const activeTournaments = getActiveTournaments();

            if (activeTournaments.length === 0) {
                return interaction.editReply({
                    content: "⚠️ لا توجد بطولات نشطة حاليًا."
                });
            }

            let message = "🏆 **البطولات النشطة:**\n\n";
            for (const tournament of activeTournaments) {
                message += `**${tournament.id}.** ${tournament.name} - ${tournament.status}\n`;
            }

            await interaction.editReply({ content: message });
        }
    } catch (error) {
        logger.error('خطأ في معالجة لوحة التحكم:', error);

        if (interaction.deferred) {
            await interaction.editReply({
                content: `⚠️ حدث خطأ: ${error.message}`
            });
        } else {
            await interaction.reply({
                content: `⚠️ حدث خطأ: ${error.message}`,
                ephemeral: true
            });
        }
    }
}

/**
 * معالجة أمر ترتيب البطولة
 */
async function handleLeaderboard(interaction) {
    await interaction.reply({
        content: '⚠️ هذه الميزة قيد التطوير. استخدم الأوامر الأصلية حاليًا.',
        ephemeral: true
    });
}

/**
 * معالجة أمر معلومات البطولة
 */
async function handleInfo(interaction) {
    await interaction.reply({
        content: '⚠️ هذه الميزة قيد التطوير. استخدم الأوامر الأصلية حاليًا.',
        ephemeral: true
    });
}

// تصدير الأمر
module.exports = {
    data: commandData,
    execute
};
