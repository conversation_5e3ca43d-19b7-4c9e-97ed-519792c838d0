/**
 * إدارة نظام المواسم
 */

const logger = require('../core/logger');
const { data, saveAllData } = require('../core/database');
const { formatDate } = require('../core/utils');

/**
 * بدء موسم جديد
 * @param {Object} options - خيارات الموسم الجديد
 * @returns {Object} - معلومات العملية
 */
function startNewSeason(options) {
    try {
        const { resetScores = true, keepStats = true, createdBy } = options;

        // حفظ معلومات الموسم الحالي في التاريخ
        const currentSeason = {
            number: data.seasons.current,
            startDate: data.seasons.startDate,
            endDate: new Date().toISOString(),
            scores: { ...data.scores },
            tkfatScores: { ...data.tkfatScores },
            tournaments: {
                completed: data.tournaments.history.filter(t => t.status === "completed"),
                count: data.tournaments.history.filter(t => t.status === "completed").length
            },
            createdBy
        };

        // إضافة الموسم الحالي إلى التاريخ
        data.seasons.history.push(currentSeason);

        // زيادة رقم الموسم
        data.seasons.current++;

        // تعيين تاريخ بدء الموسم الجديد
        data.seasons.startDate = new Date().toISOString();

        // إعادة تعيين النقاط إذا تم طلب ذلك
        if (resetScores) {
            data.scores = {};
            data.tkfatScores = {};
        }

        // إعادة تعيين الإحصائيات إذا لم يتم طلب الاحتفاظ بها
        if (!keepStats) {
            data.playerStats = {};
        }

        // إعادة تعيين البطولات النشطة
        data.tournaments.active = [];

        // حفظ البيانات
        saveAllData();

        logger.success(`تم بدء الموسم الجديد رقم ${data.seasons.current}`);

        return {
            success: true,
            message: `تم بدء الموسم الجديد رقم ${data.seasons.current} بنجاح`,
            newSeason: {
                number: data.seasons.current,
                startDate: data.seasons.startDate,
                resetScores,
                keepStats
            },
            previousSeason: currentSeason
        };
    } catch (error) {
        logger.error(`خطأ في بدء موسم جديد:`, error);
        return { success: false, message: `حدث خطأ: ${error.message}` };
    }
}

/**
 * الحصول على إحصائيات الموسم
 * @param {number} seasonNumber - رقم الموسم (اختياري، الموسم الحالي افتراضيًا)
 * @param {Object} guild - كائن السيرفر
 * @returns {Object} - إحصائيات الموسم
 */
async function getSeasonStats(seasonNumber, guild) {
    try {
        // تحديد الموسم المطلوب
        const targetSeason = seasonNumber || data.seasons.current;

        // التحقق من وجود الموسم
        if (targetSeason > data.seasons.current) {
            return { success: false, message: `الموسم رقم ${targetSeason} غير موجود` };
        }

        let seasonData;
        let scores;
        let tkfatScores;
        let startDate;
        let endDate;

        // الحصول على بيانات الموسم
        if (targetSeason === data.seasons.current) {
            // الموسم الحالي
            scores = data.scores;
            tkfatScores = data.tkfatScores;
            startDate = data.seasons.startDate;
            endDate = new Date().toISOString();

            // البطولات المكتملة في الموسم الحالي
            const completedTournaments = data.tournaments.history.filter(t =>
                t.status === "completed" &&
                new Date(t.completedAt) >= new Date(startDate)
            );

            seasonData = {
                number: targetSeason,
                startDate,
                endDate,
                scores,
                tkfatScores,
                tournaments: {
                    completed: completedTournaments,
                    count: completedTournaments.length
                }
            };
        } else {
            // موسم سابق
            seasonData = data.seasons.history.find(s => s.number === targetSeason);

            if (!seasonData) {
                return { success: false, message: `لم يتم العثور على بيانات الموسم رقم ${targetSeason}` };
            }

            scores = seasonData.scores;
            tkfatScores = seasonData.tkfatScores;
            startDate = seasonData.startDate;
            endDate = seasonData.endDate;
        }

        // حساب المدة
        const seasonDuration = Math.floor((new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24));

        // الحصول على أفضل اللاعبين
        const topPlayers = await Promise.all(
            Object.entries(scores)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 10)
                .map(async ([userId, points]) => {
                    let playerName = "Unknown User";
                    try {
                        const member = await guild.members.fetch(userId);
                        playerName = member.displayName || member.user.username;
                    } catch (error) {
                        console.log(`لا يمكن العثور على العضو: ${userId}`);
                    }
                    return {
                        id: userId,
                        name: playerName,
                        points
                    };
                })
        );

        // الحصول على أفضل اللاعبين في نقاط التكفات
        const topTkfatPlayers = await Promise.all(
            Object.entries(tkfatScores)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 5)
                .map(async ([userId, points]) => {
                    let playerName = "Unknown User";
                    try {
                        const member = await guild.members.fetch(userId);
                        playerName = member.displayName || member.user.username;
                    } catch (error) {
                        console.log(`لا يمكن العثور على العضو: ${userId}`);
                    }
                    return {
                        id: userId,
                        name: playerName,
                        points
                    };
                })
        );

        // حساب إجمالي عدد اللاعبين
        const totalPlayers = Object.keys(scores).length;

        // حساب إجمالي النقاط
        const totalPoints = Object.values(scores).reduce((sum, points) => sum + points, 0);

        // حساب إجمالي نقاط التكفات
        const totalTkfatPoints = Object.values(tkfatScores).reduce((sum, points) => sum + points, 0);

        // إنشاء الإحصائيات
        const stats = {
            seasonNumber: targetSeason,
            startDate,
            endDate,
            duration: seasonDuration,
            totalPlayers,
            totalPoints,
            totalTkfatPoints,
            topPlayers,
            topTkfatPlayers,
            tournaments: seasonData.tournaments
        };

        return {
            success: true,
            stats
        };
    } catch (error) {
        logger.error(`خطأ في الحصول على إحصائيات الموسم:`, error);
        return { success: false, message: `حدث خطأ: ${error.message}` };
    }
}

/**
 * الحصول على تاريخ المواسم مع إحصائيات محسنة
 * @param {number} limit - عدد المواسم المطلوب عرضها
 * @returns {Object} - تاريخ المواسم
 */
function getSeasonsHistory(limit = 10) {
    try {
        // إنشاء قائمة بجميع المواسم (السابقة والحالي)
        const allSeasons = [
            ...data.seasons.history,
            {
                number: data.seasons.current,
                startDate: data.seasons.startDate,
                endDate: null,
                active: true,
                stats: calculateCurrentSeasonStats(),
                topPlayers: getCurrentTopPlayers(5)
            }
        ];

        // ترتيب المواسم من الأحدث إلى الأقدم
        allSeasons.sort((a, b) => b.number - a.number);

        // تحديد العدد المطلوب عرضه
        const limitedSeasons = allSeasons.slice(0, limit);

        // تنسيق المواسم مع معلومات إضافية
        const formattedSeasons = limitedSeasons.map(season => ({
            number: season.number,
            startDate: formatDate(season.startDate),
            endDate: season.endDate ? formatDate(season.endDate) : "جاري",
            active: !!season.active,
            duration: season.endDate
                ? Math.floor((new Date(season.endDate) - new Date(season.startDate)) / (1000 * 60 * 60 * 24))
                : Math.floor((new Date() - new Date(season.startDate)) / (1000 * 60 * 60 * 24)),
            topPlayers: season.topPlayers || [],
            stats: season.stats || {}
        }));

        return {
            success: true,
            seasons: formattedSeasons,
            currentSeason: data.seasons.current,
            totalSeasons: allSeasons.length
        };
    } catch (error) {
        logger.error(`خطأ في الحصول على تاريخ المواسم:`, error);
        return { success: false, message: `حدث خطأ: ${error.message}` };
    }
}

/**
 * حساب إحصائيات الموسم الحالي
 * @returns {Object} - إحصائيات الموسم
 */
function calculateCurrentSeasonStats() {
    try {
        // عدد اللاعبين النشطين
        const booyahPlayers = Object.keys(data.scores).length;
        const tkfatPlayers = Object.keys(data.tkfatScores).length;
        const uniquePlayers = new Set([...Object.keys(data.scores), ...Object.keys(data.tkfatScores)]).size;

        // إجمالي النقاط
        const totalBooyahPoints = Object.values(data.scores).reduce((sum, points) => sum + points, 0);
        const totalTkfatPoints = Object.values(data.tkfatScores).reduce((sum, points) => sum + points, 0);

        // البطولات في هذا الموسم
        const seasonStartDate = new Date(data.seasons.startDate);
        const completedTournaments = data.tournaments.history.filter(t =>
            t.status === 'completed' &&
            new Date(t.createdAt) >= seasonStartDate
        ).length;

        const activeTournaments = data.tournaments.active.length;

        // متوسط النقاط
        const avgBooyahPoints = uniquePlayers > 0 ? Math.round(totalBooyahPoints / uniquePlayers) : 0;
        const avgTkfatPoints = uniquePlayers > 0 ? Math.round(totalTkfatPoints / uniquePlayers) : 0;

        return {
            players: {
                total: uniquePlayers,
                booyahPlayers,
                tkfatPlayers
            },
            points: {
                totalBooyah: totalBooyahPoints,
                totalTkfat: totalTkfatPoints,
                avgBooyah: avgBooyahPoints,
                avgTkfat: avgTkfatPoints
            },
            tournaments: {
                completed: completedTournaments,
                active: activeTournaments,
                total: completedTournaments + activeTournaments
            }
        };
    } catch (error) {
        logger.error('خطأ في حساب إحصائيات الموسم الحالي:', error);
        return {
            players: { total: 0, booyahPlayers: 0, tkfatPlayers: 0 },
            points: { totalBooyah: 0, totalTkfat: 0, avgBooyah: 0, avgTkfat: 0 },
            tournaments: { completed: 0, active: 0, total: 0 }
        };
    }
}

/**
 * الحصول على أفضل اللاعبين في الموسم الحالي
 * @param {number} limit - عدد اللاعبين المطلوب
 * @returns {Array} - قائمة أفضل اللاعبين
 */
function getCurrentTopPlayers(limit = 10) {
    try {
        // جمع جميع اللاعبين مع نقاطهم
        const allPlayers = [];

        // إضافة لاعبي البوياه
        for (const [userId, booyahPoints] of Object.entries(data.scores)) {
            const tkfatPoints = data.tkfatScores[userId] || 0;
            const totalPoints = booyahPoints + tkfatPoints;

            allPlayers.push({
                userId,
                booyahPoints,
                tkfatPoints,
                totalPoints,
                ratio: tkfatPoints > 0 ? (booyahPoints / tkfatPoints).toFixed(2) : booyahPoints
            });
        }

        // إضافة لاعبي التكفات الذين ليس لديهم بوياه
        for (const [userId, tkfatPoints] of Object.entries(data.tkfatScores)) {
            if (!data.scores[userId]) {
                allPlayers.push({
                    userId,
                    booyahPoints: 0,
                    tkfatPoints,
                    totalPoints: tkfatPoints,
                    ratio: 0
                });
            }
        }

        // ترتيب حسب إجمالي النقاط
        allPlayers.sort((a, b) => b.totalPoints - a.totalPoints);

        return allPlayers.slice(0, limit);
    } catch (error) {
        logger.error('خطأ في الحصول على أفضل اللاعبين:', error);
        return [];
    }
}

// تصدير الدوال
module.exports = {
    startNewSeason,
    getSeasonStats,
    getSeasonsHistory,
    calculateCurrentSeasonStats,
    getCurrentTopPlayers
};
