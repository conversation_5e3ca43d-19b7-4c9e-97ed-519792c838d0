/**
 * إدارة نظام النقاط
 */

const logger = require('../core/logger');
const { data, saveAllData } = require('../core/database');
const { LEVEL_ROLES, LEVEL_BADGES, LEVEL_NAMES } = require('../../config/constants');

/**
 * إضافة نقاط بوياه للاعب
 * @param {string} userId - معرف اللاعب
 * @param {number} points - عدد النقاط
 * @param {string} reason - سبب الإضافة
 * @returns {Object} - معلومات العملية
 */
function addBooyahPoints(userId, points, reason = "غير محدد") {
    try {
        // التحقق من صحة المعلومات
        if (!userId) {
            return { success: false, message: "معرف اللاعب مطلوب" };
        }

        points = parseInt(points);
        if (isNaN(points) || points <= 0) {
            return { success: false, message: "عدد النقاط يجب أن يكون رقمًا موجبًا" };
        }

        // إضافة النقاط
        data.scores[userId] = (data.scores[userId] || 0) + points;

        // تحديث إحصائيات اللاعب
        if (!data.playerStats[userId]) {
            data.playerStats[userId] = {
                wins: 0,
                losses: 0,
                totalMatches: 0,
                tournamentWins: 0,
                matchHistory: []
            };
        }

        // إضافة سجل للعملية
        data.playerStats[userId].pointsHistory = data.playerStats[userId].pointsHistory || [];
        data.playerStats[userId].pointsHistory.push({
            timestamp: new Date().toISOString(),
            points: points,
            type: "add",
            reason: reason,
            newTotal: data.scores[userId]
        });

        // حفظ البيانات
        saveAllData();

        logger.success(`تمت إضافة ${points} نقطة بوياه للاعب ${userId}. السبب: ${reason}`);

        return {
            success: true,
            message: `تمت إضافة ${points} نقطة بوياه بنجاح`,
            newTotal: data.scores[userId]
        };
    } catch (error) {
        logger.error(`خطأ في إضافة نقاط بوياه للاعب ${userId}:`, error);
        return { success: false, message: `حدث خطأ: ${error.message}` };
    }
}

/**
 * إزالة نقاط بوياه من لاعب
 * @param {string} userId - معرف اللاعب
 * @param {number} points - عدد النقاط
 * @param {string} reason - سبب الإزالة
 * @returns {Object} - معلومات العملية
 */
function removeBooyahPoints(userId, points, reason = "غير محدد") {
    try {
        // التحقق من صحة المعلومات
        if (!userId) {
            return { success: false, message: "معرف اللاعب مطلوب" };
        }

        points = parseInt(points);
        if (isNaN(points) || points <= 0) {
            return { success: false, message: "عدد النقاط يجب أن يكون رقمًا موجبًا" };
        }

        // التحقق من وجود نقاط كافية
        const currentPoints = data.scores[userId] || 0;
        if (currentPoints < points) {
            return {
                success: false,
                message: `اللاعب لديه ${currentPoints} نقطة فقط، لا يمكن إزالة ${points} نقطة`
            };
        }

        // إزالة النقاط
        data.scores[userId] = currentPoints - points;

        // تحديث إحصائيات اللاعب
        if (!data.playerStats[userId]) {
            data.playerStats[userId] = {
                wins: 0,
                losses: 0,
                totalMatches: 0,
                tournamentWins: 0,
                matchHistory: []
            };
        }

        // إضافة سجل للعملية
        data.playerStats[userId].pointsHistory = data.playerStats[userId].pointsHistory || [];
        data.playerStats[userId].pointsHistory.push({
            timestamp: new Date().toISOString(),
            points: points,
            type: "remove",
            reason: reason,
            newTotal: data.scores[userId]
        });

        // حفظ البيانات
        saveAllData();

        logger.success(`تمت إزالة ${points} نقطة بوياه من اللاعب ${userId}. السبب: ${reason}`);

        return {
            success: true,
            message: `تمت إزالة ${points} نقطة بوياه بنجاح`,
            newTotal: data.scores[userId]
        };
    } catch (error) {
        logger.error(`خطأ في إزالة نقاط بوياه من اللاعب ${userId}:`, error);
        return { success: false, message: `حدث خطأ: ${error.message}` };
    }
}

/**
 * إضافة نقاط تكفات للاعب
 * @param {string} userId - معرف اللاعب
 * @param {number} points - عدد النقاط
 * @param {string} reason - سبب الإضافة
 * @returns {Object} - معلومات العملية
 */
function addTkfatPoints(userId, points, reason = "غير محدد") {
    try {
        // التحقق من صحة المعلومات
        if (!userId) {
            return { success: false, message: "معرف اللاعب مطلوب" };
        }

        points = parseInt(points);
        if (isNaN(points) || points <= 0) {
            return { success: false, message: "عدد النقاط يجب أن يكون رقمًا موجبًا" };
        }

        // إضافة النقاط
        data.tkfatScores[userId] = (data.tkfatScores[userId] || 0) + points;

        // تحديث إحصائيات اللاعب
        if (!data.playerStats[userId]) {
            data.playerStats[userId] = {
                wins: 0,
                losses: 0,
                totalMatches: 0,
                tournamentWins: 0,
                matchHistory: []
            };
        }

        // إضافة سجل للعملية
        data.playerStats[userId].tkfatPointsHistory = data.playerStats[userId].tkfatPointsHistory || [];
        data.playerStats[userId].tkfatPointsHistory.push({
            timestamp: new Date().toISOString(),
            points: points,
            type: "add",
            reason: reason,
            newTotal: data.tkfatScores[userId]
        });

        // حفظ البيانات
        saveAllData();

        logger.success(`تمت إضافة ${points} نقطة تكفات للاعب ${userId}. السبب: ${reason}`);

        return {
            success: true,
            message: `تمت إضافة ${points} نقطة تكفات بنجاح`,
            newTotal: data.tkfatScores[userId]
        };
    } catch (error) {
        logger.error(`خطأ في إضافة نقاط تكفات للاعب ${userId}:`, error);
        return { success: false, message: `حدث خطأ: ${error.message}` };
    }
}

/**
 * الحصول على مستوى اللاعب بناءً على نقاط البوياه
 * @param {string} userId - معرف اللاعب
 * @returns {Object} - معلومات المستوى
 */
function getPlayerLevel(userId) {
    const points = data.scores[userId] || 0;

    // البحث عن المستوى المناسب
    let level = 0;
    let badge = LEVEL_BADGES[0];
    let name = LEVEL_NAMES[0];

    // الحصول على قائمة مرتبة من عتبات المستويات
    const thresholds = Object.keys(LEVEL_BADGES)
        .map(Number)
        .sort((a, b) => a - b);

    // البحث عن أعلى عتبة أقل من أو تساوي نقاط اللاعب
    for (const threshold of thresholds) {
        if (points >= threshold) {
            level = threshold;
            badge = LEVEL_BADGES[threshold];
            name = LEVEL_NAMES[threshold];
        } else {
            break;
        }
    }

    // الحصول على المستوى التالي
    const nextLevelIndex = thresholds.findIndex(t => t === level) + 1;
    const nextLevel = nextLevelIndex < thresholds.length ? thresholds[nextLevelIndex] : null;
    const pointsToNextLevel = nextLevel ? nextLevel - points : null;

    return {
        points,
        level,
        badge,
        name,
        nextLevel,
        pointsToNextLevel
    };
}

/**
 * الحصول على قائمة المتصدرين في نقاط البوياه
 * @param {number} limit - عدد اللاعبين المطلوب عرضهم
 * @returns {Array} - قائمة المتصدرين
 */
function getBooyahLeaderboard(limit = 10) {
    return Object.entries(data.scores)
        .map(([userId, points]) => ({ userId, points }))
        .sort((a, b) => b.points - a.points)
        .slice(0, limit);
}

/**
 * إزالة نقاط تكفات من لاعب
 * @param {string} userId - معرف اللاعب
 * @param {number} points - عدد النقاط
 * @param {string} reason - سبب الإزالة
 * @returns {Object} - معلومات العملية
 */
function removeTkfatPoints(userId, points, reason = "غير محدد") {
    try {
        // التحقق من صحة المعلومات
        if (!userId) {
            return { success: false, message: "معرف اللاعب مطلوب" };
        }

        points = parseInt(points);
        if (isNaN(points) || points <= 0) {
            return { success: false, message: "عدد النقاط يجب أن يكون رقمًا موجبًا" };
        }

        // التحقق من وجود نقاط كافية
        const currentPoints = data.tkfatScores[userId] || 0;
        if (currentPoints < points) {
            return {
                success: false,
                message: `اللاعب لديه ${currentPoints} نقطة تكفات فقط، لا يمكن إزالة ${points} نقطة`
            };
        }

        // إزالة النقاط
        data.tkfatScores[userId] = currentPoints - points;

        // تحديث إحصائيات اللاعب
        if (!data.playerStats[userId]) {
            data.playerStats[userId] = {
                wins: 0,
                losses: 0,
                totalMatches: 0,
                tournamentWins: 0,
                matchHistory: []
            };
        }

        // إضافة سجل للعملية
        data.playerStats[userId].tkfatPointsHistory = data.playerStats[userId].tkfatPointsHistory || [];
        data.playerStats[userId].tkfatPointsHistory.push({
            timestamp: new Date().toISOString(),
            points: points,
            type: "remove",
            reason: reason,
            newTotal: data.tkfatScores[userId]
        });

        // حفظ البيانات
        saveAllData();

        logger.success(`تمت إزالة ${points} نقطة تكفات من اللاعب ${userId}. السبب: ${reason}`);

        return {
            success: true,
            message: `تمت إزالة ${points} نقطة تكفات بنجاح`,
            newTotal: data.tkfatScores[userId]
        };
    } catch (error) {
        logger.error(`خطأ في إزالة نقاط تكفات من اللاعب ${userId}:`, error);
        return { success: false, message: `حدث خطأ: ${error.message}` };
    }
}

/**
 * الحصول على قائمة المتصدرين في نقاط التكفات
 * @param {number} limit - عدد اللاعبين المطلوب عرضهم
 * @returns {Array} - قائمة المتصدرين
 */
function getTkfatLeaderboard(limit = 10) {
    return Object.entries(data.tkfatScores)
        .map(([userId, points]) => ({ userId, points }))
        .sort((a, b) => b.points - a.points)
        .slice(0, limit);
}

// تصدير الدوال
module.exports = {
    addBooyahPoints,
    removeBooyahPoints,
    addTkfatPoints,
    removeTkfatPoints,
    getPlayerLevel,
    getBooyahLeaderboard,
    getTkfatLeaderboard
};
